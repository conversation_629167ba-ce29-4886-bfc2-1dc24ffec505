using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Windows;
using System.Windows.Controls;
using Microsoft.EntityFrameworkCore;
using YouTubeCreator.Data;
using YouTubeCreator.Services;
using YouTubeCreator.Models;
using Newtonsoft.Json;

namespace YouTubeCreator
{
    public partial class MainWindow : Window
    {
        private YouTubeDbContext? _dbContext;
        private YouTubeScraper? _scraper;
        private ObservableCollection<CommentRecord>? _comments;
        private ObservableCollection<CommentRecord>? _filteredComments;

        public MainWindow()
        {
            try
            {
                InitializeComponent();

                _comments = new ObservableCollection<CommentRecord>();
                _filteredComments = new ObservableCollection<CommentRecord>();

                // Inicializar depois que a janela carregar
                this.Loaded += MainWindow_Loaded;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erro na inicialização: {ex.Message}", "Erro",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void MainWindow_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                _dbContext = new YouTubeDbContext();
                _scraper = new YouTubeScraper();

                if (CommentsListView != null)
                    CommentsListView.ItemsSource = _filteredComments;

                // Inicializar banco de dados
                InitializeDatabase();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erro ao carregar: {ex.Message}", "Erro",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void InitializeDatabase()
        {
            try
            {
                if (_dbContext != null)
                    await _dbContext.Database.EnsureCreatedAsync();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erro ao inicializar banco de dados: {ex.Message}", "Erro",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void ExtractButton_Click(object sender, RoutedEventArgs e)
        {
            // Verificar se os componentes foram inicializados
            if (UrlTextBox == null || _dbContext == null || _scraper == null)
            {
                MessageBox.Show("Aplicação não foi inicializada corretamente. Tente reiniciar.", "Erro de Inicialização",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                return;
            }

            var url = UrlTextBox.Text?.Trim();
            if (string.IsNullOrEmpty(url) || url == "Cole a URL do vídeo do YouTube aqui...")
            {
                MessageBox.Show("Por favor, insira uma URL do YouTube.", "URL Necessária",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                // Mostrar progresso
                ProgressBar.Visibility = Visibility.Visible;
                ProgressBar.IsIndeterminate = true;
                ExtractButton.IsEnabled = false;

                // Verificar se o vídeo já existe no banco
                var videoId = ExtractVideoIdFromUrl(url);
                VideoRecord? existingVideo = null;
                if (_dbContext != null)
                    existingVideo = await _dbContext.Videos.FirstOrDefaultAsync(v => v.VideoId == videoId);
                
                if (existingVideo != null)
                {
                    var result = MessageBox.Show(
                        "Este vídeo já foi extraído anteriormente. Deseja extrair novamente?",
                        "Vídeo Existente",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Question);
                    
                    if (result == MessageBoxResult.No)
                    {
                        await LoadVideoData(existingVideo);
                        return;
                    }
                    else
                    {
                        // Remover dados antigos
                        _dbContext?.Videos.Remove(existingVideo);
                        var oldComments = await _dbContext?.Comments.Where(c => c.VideoId == videoId).ToListAsync();
                        _dbContext?.Comments.RemoveRange(oldComments);
                        await _dbContext?.SaveChangesAsync();
                    }
                }

                // Extrair informações do vídeo
                var videoInfo = await _scraper.ExtractVideoInfoAsync(url);
                
                // Extrair comentários
                var comments = await _scraper.ExtractCommentsAsync(url, -1); // Todos os comentários
                
                // Salvar no banco de dados
                await SaveToDatabase(videoInfo, comments);
                
                // Carregar dados na interface
                VideoRecord? savedVideo = null;
                if (_dbContext != null)
                    savedVideo = await _dbContext.Videos.FirstOrDefaultAsync(v => v.VideoId == videoInfo.VideoId);
                if (savedVideo != null)
                {
                    await LoadVideoData(savedVideo);
                }

                MessageBox.Show($"Dados extraídos com sucesso!\n\nVídeo: {videoInfo.Title}\nComentários: {comments.Count}", 
                    "Extração Concluída", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erro durante a extração: {ex.Message}", "Erro", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                ProgressBar.Visibility = Visibility.Collapsed;
                ExtractButton.IsEnabled = true;
            }
        }

        private async Task SaveToDatabase(YouTubeVideoInfo videoInfo, List<YouTubeComment> comments)
        {
            var videoRecord = new VideoRecord
            {
                VideoId = videoInfo.VideoId,
                Title = videoInfo.Title,
                Description = videoInfo.Description,
                ChannelName = videoInfo.ChannelName,
                ChannelId = videoInfo.ChannelId,
                ViewCount = videoInfo.ViewCount,
                LikeCount = videoInfo.LikeCount,
                PublishDate = videoInfo.PublishDate,
                Duration = videoInfo.Duration,
                Tags = JsonConvert.SerializeObject(videoInfo.Tags),
                ThumbnailUrl = videoInfo.ThumbnailUrl,
                VideoUrl = videoInfo.VideoUrl,
                Transcript = ExtractTranscriptFromDescription(videoInfo.Description),
                ExtractedAt = DateTime.Now,
                TotalComments = comments.Count
            };

            _dbContext.Videos.Add(videoRecord);
            await _dbContext.SaveChangesAsync();

            // Salvar comentários
            foreach (var comment in comments)
            {
                var commentRecord = new CommentRecord
                {
                    VideoId = videoInfo.VideoId,
                    Author = comment.Author,
                    AuthorChannelId = comment.AuthorChannelId,
                    Text = comment.Text,
                    LikeCount = comment.LikeCount,
                    PublishDate = comment.PublishDate,
                    IsReply = comment.IsReply
                };

                _dbContext.Comments.Add(commentRecord);
            }

            await _dbContext.SaveChangesAsync();
        }

        private string ExtractTranscriptFromDescription(string description)
        {
            // Extrair transcrição da descrição se estiver lá
            var transcriptStart = description.IndexOf("=== TRANSCRIÇÃO ===");
            if (transcriptStart >= 0)
            {
                return description.Substring(transcriptStart + "=== TRANSCRIÇÃO ===".Length).Trim();
            }
            return "";
        }

        private async Task LoadVideoData(VideoRecord video)
        {
            // Carregar informações do vídeo
            LoadVideoInfo(video);
            
            // Carregar transcrição
            TranscriptTextBox.Text = video.Transcript;
            
            // Carregar comentários
            var comments = await _dbContext.Comments
                .Where(c => c.VideoId == video.VideoId)
                .OrderByDescending(c => c.LikeCount)
                .ToListAsync();
            
            _comments.Clear();
            foreach (var comment in comments)
            {
                _comments.Add(comment);
            }
            
            FilterComments("");
            CommentsCountText.Text = $"{comments.Count} comentários";
        }

        private void LoadVideoInfo(VideoRecord video)
        {
            VideoInfoPanel.Children.Clear();
            
            var tags = JsonConvert.DeserializeObject<List<string>>(video.Tags) ?? new List<string>();
            
            var infoItems = new[]
            {
                ("Título", video.Title),
                ("Canal", video.ChannelName),
                ("URL", video.VideoUrl),
                ("Visualizações", video.ViewCount.ToString("N0")),
                ("Likes", video.LikeCount.ToString("N0")),
                ("Data de Publicação", video.PublishDate.ToString("dd/MM/yyyy HH:mm")),
                ("Duração", video.Duration.ToString()),
                ("Total de Comentários", video.TotalComments.ToString("N0")),
                ("Extraído em", video.ExtractedAt.ToString("dd/MM/yyyy HH:mm")),
                ("Tags", string.Join(", ", tags.Take(10)) + (tags.Count > 10 ? $" ... (+{tags.Count - 10})" : ""))
            };

            foreach (var (label, value) in infoItems)
            {
                var panel = new StackPanel { Margin = new Thickness(0, 10, 0, 0) };
                
                var labelBlock = new TextBlock
                {
                    Text = label + ":",
                    FontWeight = FontWeights.Bold,
                    Margin = new Thickness(0, 0, 0, 5)
                };
                
                var valueBlock = new TextBlock
                {
                    Text = value,
                    TextWrapping = TextWrapping.Wrap
                };
                
                panel.Children.Add(labelBlock);
                panel.Children.Add(valueBlock);
                VideoInfoPanel.Children.Add(panel);
            }

            // Adicionar descrição separadamente
            if (!string.IsNullOrEmpty(video.Description))
            {
                var descPanel = new StackPanel { Margin = new Thickness(0, 20, 0, 0) };
                
                var descLabel = new TextBlock
                {
                    Text = "Descrição:",
                    FontWeight = FontWeights.Bold,
                    Margin = new Thickness(0, 0, 0, 5)
                };
                
                var descText = new TextBox
                {
                    Text = video.Description.Replace("=== TRANSCRIÇÃO ===", "").Trim(),
                    TextWrapping = TextWrapping.Wrap,
                    IsReadOnly = true,
                    Background = System.Windows.Media.Brushes.Transparent,
                    BorderThickness = new Thickness(0),
                    MaxHeight = 200,
                    VerticalScrollBarVisibility = ScrollBarVisibility.Auto
                };
                
                descPanel.Children.Add(descLabel);
                descPanel.Children.Add(descText);
                VideoInfoPanel.Children.Add(descPanel);
            }
        }

        private void SearchCommentsBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            var searchText = SearchCommentsBox.Text;
            FilterComments(searchText);
        }

        private void FilterComments(string searchText)
        {
            _filteredComments.Clear();
            
            var filtered = string.IsNullOrEmpty(searchText) 
                ? _comments 
                : _comments.Where(c => c.Text.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                                      c.Author.Contains(searchText, StringComparison.OrdinalIgnoreCase));
            
            foreach (var comment in filtered)
            {
                _filteredComments.Add(comment);
            }
        }

        private void ViewHistoryButton_Click(object sender, RoutedEventArgs e)
        {
            var historyWindow = new HistoryWindow(_dbContext);
            historyWindow.VideoSelected += async (selectedVideo) =>
            {
                await LoadVideoData(selectedVideo);
                UrlTextBox.Text = selectedVideo.VideoUrl;
            };
            historyWindow.ShowDialog();
        }

        private string ExtractVideoIdFromUrl(string url)
        {
            var patterns = new[]
            {
                @"(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([a-zA-Z0-9_-]{11})",
                @"youtube\.com\/watch\?.*v=([a-zA-Z0-9_-]{11})"
            };

            foreach (var pattern in patterns)
            {
                var match = System.Text.RegularExpressions.Regex.Match(url, pattern);
                if (match.Success)
                    return match.Groups[1].Value;
            }

            return "";
        }

        private void UrlTextBox_GotFocus(object sender, RoutedEventArgs e)
        {
            if (UrlTextBox.Text == "Cole a URL do vídeo do YouTube aqui...")
            {
                UrlTextBox.Text = "";
                UrlTextBox.Foreground = System.Windows.Media.Brushes.Black;
            }
        }

        private void UrlTextBox_LostFocus(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(UrlTextBox.Text))
            {
                UrlTextBox.Text = "Cole a URL do vídeo do YouTube aqui...";
                UrlTextBox.Foreground = System.Windows.Media.Brushes.Gray;
            }
        }

        private void SearchCommentsBox_GotFocus(object sender, RoutedEventArgs e)
        {
            if (SearchCommentsBox.Text == "Buscar comentários...")
            {
                SearchCommentsBox.Text = "";
                SearchCommentsBox.Foreground = System.Windows.Media.Brushes.Black;
            }
        }

        private void SearchCommentsBox_LostFocus(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(SearchCommentsBox.Text))
            {
                SearchCommentsBox.Text = "Buscar comentários...";
                SearchCommentsBox.Foreground = System.Windows.Media.Brushes.Gray;
            }
        }

        protected override void OnClosing(CancelEventArgs e)
        {
            _dbContext?.Dispose();
            _scraper?.Dispose();
            base.OnClosing(e);
        }
    }
}
