import streamlit as st
import pandas as pd
import json
from datetime import datetime
from database import YouTubeDatabase
from youtube_extractor import YouTubeExtractor
import time

# Configuração da página
st.set_page_config(
    page_title="🎬 YouTube Data Extractor",
    page_icon="🎬",
    layout="wide",
    initial_sidebar_state="expanded"
)

# CSS customizado
st.markdown("""
<style>
    .main-header {
        font-size: 3rem;
        color: #FF0000;
        text-align: center;
        margin-bottom: 2rem;
    }
    .stats-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #FF0000;
    }
    .success-message {
        background-color: #d4edda;
        color: #155724;
        padding: 1rem;
        border-radius: 0.5rem;
        border: 1px solid #c3e6cb;
    }
    .comment-main {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        margin: 10px 0;
        border-left: 3px solid #007bff;
    }
    .comment-reply {
        background-color: #e9ecef;
        padding: 10px;
        border-radius: 6px;
        margin: 8px 0 8px 20px;
    }
    .comment-tags {
        margin-top: 8px;
    }
    .tag {
        background-color: #e3f2fd;
        color: #1976d2;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 0.75em;
        margin-right: 5px;
        display: inline-block;
        border: 1px solid #bbdefb;
    }
        border-left: 2px solid #6c757d;
    }
    .comment-author {
        font-weight: bold;
        color: #495057;
    }
    .comment-stats {
        color: #6c757d;
        font-size: 0.9em;
    }
    .comment-text {
        margin: 8px 0;
        line-height: 1.4;
    }
</style>
""", unsafe_allow_html=True)

# Inicializar componentes
@st.cache_resource
def init_components():
    db = YouTubeDatabase()
    extractor = YouTubeExtractor()
    return db, extractor

def display_video_data(db, video_id, extractor):
    """Exibe todos os dados de um vídeo já extraído"""

    # Buscar dados no banco
    video_data = db.get_video_data(video_id)

    if not video_data:
        st.error("❌ Vídeo não encontrado no banco de dados!")
        return

    video_info = video_data['video']
    comments = video_data['comments']
    transcript = video_data['transcript']

    # Header do vídeo
    st.subheader(f"🎬 {video_info[2]}")  # título

    # Métricas principais
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.metric("👁️ Visualizações", extractor.format_number(video_info[8]))

    with col2:
        st.metric("👍 Likes", extractor.format_number(video_info[9]))

    with col3:
        st.metric("💬 Comentários", f"{len(comments):,}")

    with col4:
        st.metric("⏱️ Duração", extractor.format_duration(video_info[7]))

    # Informações básicas
    col1, col2 = st.columns(2)

    with col1:
        st.write(f"**📺 Canal:** {video_info[4]}")  # channel_name
        st.write(f"**📅 Publicado:** {video_info[11]}")  # upload_date
        st.write(f"**🆔 ID:** {video_info[1]}")  # video_id

    with col2:
        st.write(f"**🌐 Idioma:** {video_info[16]}")  # language
        st.write(f"**📝 Transcrição:** {'✅ Sim' if transcript else '❌ Não'}")
        st.write(f"**📊 Extraído:** {video_info[17]}")  # extracted_at

    # Abas com dados detalhados
    tab1, tab2, tab3, tab4, tab5 = st.tabs(["📋 INFORMAÇÕES", "📝 TRANSCRIÇÃO", "💬 COMENTÁRIOS", "🏷️ TAGS", "📤 EXPORTAR"])

    with tab1:

        # Descrição
        if video_info[3]:  # description
            st.subheader("📝 Descrição")
            st.text_area("", video_info[3], height=200, disabled=True)

        # Tags
        if video_info[12]:  # tags (JSON)
            try:
                tags = json.loads(video_info[12])
                if tags:
                    st.subheader("🏷️ Tags")
                    st.write(", ".join(tags[:20]))  # Primeiras 20 tags
            except:
                pass

        # Categorias
        if video_info[13]:  # categories (JSON)
            try:
                categories = json.loads(video_info[13])
                if categories:
                    st.subheader("📂 Categorias")
                    st.write(", ".join(categories))
            except:
                pass

        # Thumbnail
        if video_info[14]:  # thumbnail_url
            st.subheader("🖼️ Thumbnail")
            st.image(video_info[14], width=300)

    with tab2:
        st.subheader("📝 Transcrição")

        if transcript:
            st.success(f"✅ Transcrição disponível ({len(transcript)} segmentos)")

            # Opções de visualização
            view_mode = st.radio(
                "Modo de visualização:",
                ["📄 Texto completo", "⏱️ Com timestamps", "📊 Segmentos individuais"],
                key=f"view_mode_{video_id}"
            )

            if view_mode == "📄 Texto completo":
                # Texto completo
                full_text = " ".join([seg[3] for seg in transcript])  # text
                st.text_area("Transcrição completa:", full_text, height=400, disabled=True)

                # Estatísticas
                col1, col2, col3 = st.columns(3)
                with col1:
                    st.metric("📝 Palavras", len(full_text.split()))
                with col2:
                    st.metric("⏱️ Segmentos", len(transcript))
                with col3:
                    avg_confidence = sum(seg[6] for seg in transcript) / len(transcript)  # confidence
                    st.metric("🎯 Confiança", f"{avg_confidence:.1%}")

            elif view_mode == "⏱️ Com timestamps":
                # Com timestamps
                transcript_with_time = []
                for seg in transcript:
                    start_time = int(seg[4])  # start_time
                    minutes = start_time // 60
                    seconds = start_time % 60
                    timestamp = f"[{minutes:02d}:{seconds:02d}]"
                    transcript_with_time.append(f"{timestamp} {seg[3]}")

                full_text_with_time = "\n".join(transcript_with_time)
                st.text_area("Transcrição com timestamps:", full_text_with_time, height=400, disabled=True)

            else:  # Segmentos individuais
                # Filtros
                col1, col2 = st.columns(2)
                with col1:
                    search_transcript = st.text_input("🔍 Buscar na transcrição:", key=f"search_transcript_{video_id}")
                with col2:
                    max_segments = st.slider("Máximo de segmentos", 10, len(transcript), min(50, len(transcript)), key=f"max_segments_{video_id}")

                # Filtrar segmentos
                filtered_segments = transcript
                if search_transcript:
                    filtered_segments = [seg for seg in transcript if search_transcript.lower() in seg[3].lower()]

                # Mostrar segmentos
                for i, seg in enumerate(filtered_segments[:max_segments]):
                    start_time = int(seg[4])  # start_time
                    minutes = start_time // 60
                    seconds = start_time % 60

                    with st.expander(f"⏱️ {minutes:02d}:{seconds:02d} - Segmento {i+1}"):
                        st.write(seg[3])  # text
                        st.caption(f"Confiança: {seg[6]:.1%} | Duração: {seg[5]:.1f}s")  # confidence, duration
        else:
            st.warning("⚠️ Transcrição não disponível para este vídeo")

            # Botão para tentar extrair transcrição
            st.markdown("---")
            st.subheader("🔄 Tentar Extrair Transcrição")

            # Opções de extração
            extraction_method = st.radio(
                "Método de extração:",
                ["📝 Legendas do YouTube", "🎤 Whisper AI (mais robusto)"],
                help="Legendas: mais rápido, mas só funciona se existirem. Whisper: gera transcrição do áudio, mais lento mas funciona sempre.",
                key=f"method_{video_id}"
            )

            col1, col2 = st.columns([2, 1])

            with col1:
                if extraction_method == "📝 Legendas do YouTube":
                    st.info("💡 Tentará extrair legendas existentes do YouTube (rápido)")
                else:
                    st.warning("⚠️ Whisper baixa o áudio e gera transcrição")
                    st.info("📋 Requer: pip install openai-whisper")

                    # Opções do Whisper
                    col_model, col_duration = st.columns(2)

                    with col_model:
                        whisper_model = st.selectbox(
                            "Modelo Whisper:",
                            ["tiny", "base", "small", "medium", "large", "turbo"],
                            index=0,
                            help="tiny: ~10x mais rápido, base: ~7x, small: ~4x, medium: ~2x, large: 1x, turbo: ~8x",
                            key=f"whisper_model_{video_id}"
                        )

                    with col_duration:
                        max_duration = st.selectbox(
                            "Duração máxima:",
                            [300, 600, 1200, -1],
                            index=1,
                            format_func=lambda x: "5 min" if x == 300 else "10 min" if x == 600 else "20 min" if x == 1200 else "Completo",
                            help="Limitar duração para acelerar processamento",
                            key=f"max_duration_{video_id}"
                        )

                    # Estimativa de tempo
                    time_estimates = {
                        "tiny": "30s-1min",
                        "base": "1-2min",
                        "small": "2-4min",
                        "medium": "5-10min",
                        "large": "10-20min",
                        "turbo": "1-3min"
                    }
                    st.caption(f"⏱️ Tempo estimado: {time_estimates[whisper_model]}")

            with col2:
                button_text = "🎯 Extrair Legendas" if extraction_method == "📝 Legendas do YouTube" else "🎤 Gerar com Whisper"

                if st.button(button_text, type="primary", key=f"extract_transcript_{video_id}"):
                    if extraction_method == "📝 Legendas do YouTube":
                        with st.spinner("🎬 Extraindo legendas..."):
                            try:
                                transcript_result = extractor.extract_transcript(video_id)

                                if transcript_result:
                                    db.save_transcript(video_id, transcript_result)
                                    st.success("✅ Legendas extraídas e salvas com sucesso!")
                                    st.info("🔄 Recarregue a página para ver a transcrição")

                                    # Mostrar informações da transcrição
                                    if transcript_result:
                                        first_entry = transcript_result[0]
                                        lang_info = first_entry.get('language', 'desconhecido')
                                        is_auto = first_entry.get('is_auto_generated', False)

                                        col_info1, col_info2, col_info3 = st.columns(3)
                                        with col_info1:
                                            st.metric("🌐 Idioma", lang_info)
                                        with col_info2:
                                            st.metric("🤖 Tipo", "Automática" if is_auto else "Manual")
                                        with col_info3:
                                            st.metric("📝 Segmentos", len(transcript_result))

                                    preview_text = " ".join([seg['text'] for seg in transcript_result[:5]])
                                    st.text_area("Preview:", preview_text, height=100, disabled=True)
                                else:
                                    st.error("❌ Não foi possível extrair legendas")
                                    st.info("💡 Tente o método Whisper AI para gerar transcrição do áudio")
                                    st.info("🔍 Verifique os logs no terminal para mais detalhes")

                            except Exception as e:
                                st.error(f"❌ Erro ao extrair legendas: {str(e)}")

                    else:  # Whisper
                        duration_text = "5 min" if max_duration == 300 else "10 min" if max_duration == 600 else "20 min" if max_duration == 1200 else "completo"
                        with st.spinner(f"🎤 Gerando transcrição com Whisper ({whisper_model}, {duration_text})..."):
                            try:
                                transcript_result = extractor.extract_transcript_with_whisper(
                                    video_id,
                                    model_size=whisper_model,
                                    max_duration=max_duration if max_duration != -1 else 3600
                                )

                                if transcript_result:
                                    db.save_transcript(video_id, transcript_result)
                                    st.success("✅ Transcrição gerada com Whisper e salva com sucesso!")
                                    st.info("🔄 Recarregue a página para ver a transcrição")

                                    preview_text = " ".join([seg['text'] for seg in transcript_result[:5]])
                                    st.text_area("Preview:", preview_text, height=100, disabled=True)
                                else:
                                    st.error("❌ Não foi possível gerar transcrição com Whisper")
                                    st.info("💭 Verifique se o Whisper está instalado: pip install openai-whisper")

                            except Exception as e:
                                st.error(f"❌ Erro ao gerar transcrição: {str(e)}")
                                if "whisper" in str(e).lower():
                                    st.info("� Instale o Whisper: pip install openai-whisper")

    with tab3:
        st.subheader(f"💬 Comentários ({len(comments)})")

        if not comments:
            st.warning("⚠️ Nenhum comentário encontrado para este vídeo")
            return

        # Filtros com keys únicos
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            search_comments = st.text_input("🔍 Buscar nos comentários:", key=f"search_comments_{video_id}")

        with col2:
            try:
                max_likes = max(c[6] for c in comments) if comments else 0
                min_likes = st.slider("Mínimo de likes", 0, max_likes, 0, key=f"min_likes_{video_id}")
            except (IndexError, TypeError):
                st.error("Erro ao acessar likes dos comentários")
                min_likes = 0

        with col3:
            show_replies = st.checkbox("Mostrar respostas", value=True, key=f"show_replies_{video_id}")

        with col4:
            # Filtro por tags
            all_tags = set()
            for comment in comments:
                comment_id = comment[2] if len(comment) > 2 else None
                if comment_id:
                    comment_tags = db.get_comment_tags(comment_id)
                    all_tags.update(comment_tags)

            if all_tags:
                selected_tags = st.multiselect(
                    "🏷️ Filtrar por tags:",
                    options=sorted(list(all_tags)),
                    key=f"tag_filter_{video_id}",
                    help="Selecione uma ou mais tags para filtrar"
                )
            else:
                selected_tags = []
                st.write("🏷️ Sem tags")

        # Filtrar comentários com tratamento de erro
        try:
            filtered_comments = comments.copy()

            if search_comments:
                filtered_comments = [c for c in filtered_comments if search_comments.lower() in str(c[5]).lower()]

            if min_likes > 0:
                filtered_comments = [c for c in filtered_comments if c[6] >= min_likes]

            if not show_replies:
                # Filtrar respostas baseado no parent_comment_id
                filtered_comments = [c for c in filtered_comments if not (len(c) > 11 and c[11] is not None and c[11] != 'root' and c[11] != '')]

            # Filtrar por tags selecionadas
            if selected_tags:
                def comment_has_selected_tags(comment):
                    comment_id = comment[2] if len(comment) > 2 else None
                    if not comment_id:
                        return False
                    comment_tags = db.get_comment_tags(comment_id)
                    # Verificar se o comentário tem pelo menos uma das tags selecionadas
                    return any(tag in comment_tags for tag in selected_tags)

                filtered_comments = [c for c in filtered_comments if comment_has_selected_tags(c)]



        except Exception as e:
            st.error(f"Erro ao filtrar comentários: {e}")
            filtered_comments = comments

        # Organizar comentários hierarquicamente
        try:
            # Separar comentários principais de respostas
            main_comments = []
            replies = []

            for comment in filtered_comments:
                # Um comentário é resposta se tem parent_comment_id (campo 11) não nulo
                has_parent = len(comment) > 11 and comment[11] is not None and comment[11] != 'root' and comment[11] != ''

                if has_parent:
                    replies.append(comment)
                else:
                    main_comments.append(comment)

            # Mostrar estatísticas com informação sobre filtros
            stats_text = f"📊 **{len(main_comments)} comentários principais** • **{len(replies)} respostas**"

            # Adicionar informação sobre filtros ativos
            active_filters = []
            if search_comments:
                active_filters.append(f"🔍 Busca: '{search_comments}'")
            if min_likes > 0:
                active_filters.append(f"👍 Min. likes: {min_likes}")
            if not show_replies:
                active_filters.append("💬 Sem respostas")
            if selected_tags:
                active_filters.append(f"🏷️ Tags: {', '.join(selected_tags)}")

            if active_filters:
                stats_text += f" | **Filtros:** {' • '.join(active_filters)}"

            st.write(stats_text)

        except Exception as e:
            st.error(f"Erro ao organizar comentários: {e}")
            # Fallback: tratar todos como comentários principais
            main_comments = filtered_comments
            replies = []

        # Criar dicionário de respostas por comentário pai
        replies_dict = {}
        try:
            for reply in replies:
                parent_id = reply[11] if len(reply) > 11 else None
                if parent_id:
                    if parent_id not in replies_dict:
                        replies_dict[parent_id] = []
                    replies_dict[parent_id].append(reply)
        except Exception as e:
            st.warning(f"Aviso ao processar respostas: {e}")

        # Ordenar comentários principais por likes
        try:
            main_comments.sort(key=lambda x: x[6], reverse=True)
        except Exception as e:
            st.warning(f"Aviso ao ordenar comentários: {e}")

        # Paginação
        comments_per_page = st.selectbox(
            "Comentários por página:",
            [20, 50, 100, 200, "Todos"],
            index=1,
            key=f"comments_per_page_{video_id}"
        )

        if comments_per_page == "Todos":
            comments_to_show = main_comments
            current_page = 1
            total_pages = 1
        else:
            total_pages = max(1, (len(main_comments) + comments_per_page - 1) // comments_per_page)

            if total_pages > 1:
                current_page = st.number_input(
                    f"Página (1-{total_pages}):",
                    min_value=1,
                    max_value=total_pages,
                    value=1,
                    key=f"page_number_{video_id}"
                )
            else:
                current_page = 1

            start_idx = (current_page - 1) * comments_per_page
            end_idx = start_idx + comments_per_page
            comments_to_show = main_comments[start_idx:end_idx]

        # Mostrar informações da paginação
        if comments_per_page != "Todos":
            col1, col2, col3 = st.columns(3)
            with col1:
                st.metric("📄 Página", f"{current_page}/{total_pages}")
            with col2:
                st.metric("📊 Nesta página", len(comments_to_show))
            with col3:
                st.metric("📈 Total", len(main_comments))

        st.markdown("---")

        # Mostrar comentários com estrutura hierárquica
        if comments_to_show:
            for i, comment in enumerate(comments_to_show):
                try:
                    # Verificar se temos dados suficientes
                    if len(comment) < 8:
                        st.error(f"Comentário {i+1}: Dados insuficientes ({len(comment)} campos)")
                        continue

                    # Comentário principal com estilo
                    author = comment[3] if len(comment) > 3 else "Autor desconhecido"
                    likes = comment[6] if len(comment) > 6 else 0
                    replies_count = comment[7] if len(comment) > 7 else 0

                    # Formatar data
                    raw_date = comment[8] if len(comment) > 8 and comment[8] else None
                    if raw_date:
                        try:
                            # Tentar converter de timestamp ou string ISO
                            if isinstance(raw_date, (int, float)):
                                # Se for timestamp
                                from datetime import datetime
                                date = datetime.fromtimestamp(raw_date).strftime("%d/%m/%Y %H:%M")
                            elif isinstance(raw_date, str):
                                # Se for string ISO, tentar converter
                                from datetime import datetime
                                try:
                                    dt = datetime.fromisoformat(raw_date.replace('Z', '+00:00'))
                                    date = dt.strftime("%d/%m/%Y %H:%M")
                                except:
                                    # Se não conseguir converter, mostrar os primeiros 10 caracteres
                                    date = raw_date[:10] if len(raw_date) >= 10 else raw_date
                            else:
                                date = str(raw_date)
                        except:
                            date = "Data inválida"
                    else:
                        date = "Data não disponível"

                    text = comment[5] if len(comment) > 5 else "Texto não disponível"

                    # Escapar HTML no texto
                    import html
                    text_escaped = html.escape(str(text))
                    author_escaped = html.escape(str(author))
                    comment_id = comment[2] if len(comment) > 2 else None

                    # Buscar tags do comentário
                    tags = db.get_comment_tags(comment_id) if comment_id else []

                    # Container para comentário principal (SEM tags dentro)
                    st.markdown(f"""
                    <div class="comment-main">
                        <div class="comment-author">👤 {author_escaped}</div>
                        <div class="comment-stats">👍 {likes} • 💬 {replies_count} • 📅 {date}</div>
                        <div class="comment-text">{text_escaped}</div>
                    </div>
                    """, unsafe_allow_html=True)

                    # Interface para gerenciar tags
                    if comment_id:
                        # Buscar todas as tags existentes
                        all_existing_tags = set()
                        for c in comments:
                            c_id = c[2] if len(c) > 2 else None
                            if c_id:
                                c_tags = db.get_comment_tags(c_id)
                                all_existing_tags.update(c_tags)

                        tag_rules = db.get_tag_rules()
                        for rule in tag_rules:
                            all_existing_tags.add(rule['tag_name'])

                        available_tags = sorted(list(all_existing_tags - set(tags)))

                        # Layout compacto: Dropdown menor + Adicionar + Lixeira + Tags com X
                        cols = st.columns([1.5, 0.5, 0.5] + [1] * len(tags))

                        # Dropdown compacto (sempre visível)
                        with cols[0]:
                            selected_tag = st.selectbox(
                                "Tag",
                                [""] + available_tags,
                                key=f"select_tag_{comment_id}",
                                label_visibility="collapsed"
                            )

                        # Botão adicionar
                        with cols[1]:
                            if st.button("➕", key=f"add_tag_{comment_id}", help="Adicionar tag"):
                                if selected_tag:
                                    if db.add_manual_tag_to_comment(comment_id, selected_tag):
                                        st.rerun()

                        # Lixeira ao lado do adicionar
                        with cols[2]:
                            if st.button("🗑️", key=f"delete_{comment_id}", help="Excluir comentário"):
                                if db.delete_comment(comment_id):
                                    st.success("Excluído!")
                                    st.rerun()

                        # Tags atuais com botão X individual
                        for i, tag in enumerate(tags):
                            with cols[3 + i]:
                                if st.button(f"❌ {tag}", key=f"remove_tag_{comment_id}_{tag}", help=f"Remover '{tag}'"):
                                    if db.remove_tag_from_comment(comment_id, tag):
                                        st.rerun()

                    # Mostrar respostas se existirem
                    try:
                        comment_id = comment[2] if len(comment) > 2 else None
                        if comment_id and comment_id in replies_dict:
                            comment_replies = replies_dict[comment_id]
                            comment_replies.sort(key=lambda x: x[6] if len(x) > 6 else 0, reverse=True)

                            # Mostrar respostas com indentação
                            for reply in comment_replies:
                                if len(reply) >= 6:
                                    reply_author = html.escape(str(reply[3] if len(reply) > 3 else "Autor desconhecido"))
                                    reply_likes = reply[6] if len(reply) > 6 else 0

                                    # Formatar data da resposta
                                    raw_reply_date = reply[8] if len(reply) > 8 and reply[8] else None
                                    if raw_reply_date:
                                        try:
                                            # Tentar converter de timestamp ou string ISO
                                            if isinstance(raw_reply_date, (int, float)):
                                                # Se for timestamp
                                                from datetime import datetime
                                                reply_date = datetime.fromtimestamp(raw_reply_date).strftime("%d/%m/%Y %H:%M")
                                            elif isinstance(raw_reply_date, str):
                                                # Se for string ISO, tentar converter
                                                from datetime import datetime
                                                try:
                                                    dt = datetime.fromisoformat(raw_reply_date.replace('Z', '+00:00'))
                                                    reply_date = dt.strftime("%d/%m/%Y %H:%M")
                                                except:
                                                    # Se não conseguir converter, mostrar os primeiros 10 caracteres
                                                    reply_date = raw_reply_date[:10] if len(raw_reply_date) >= 10 else raw_reply_date
                                            else:
                                                reply_date = str(raw_reply_date)
                                        except:
                                            reply_date = "Data inválida"
                                    else:
                                        reply_date = "Data não disponível"

                                    reply_text = html.escape(str(reply[5] if len(reply) > 5 else "Texto não disponível"))
                                    reply_id = reply[2] if len(reply) > 2 else None

                                    # Buscar tags da resposta
                                    reply_tags = db.get_comment_tags(reply_id) if reply_id else []

                                    # Container para resposta (SEM tags dentro)
                                    st.markdown(f"""
                                    <div class="comment-reply">
                                        <div class="comment-author">↳ 👤 {reply_author}</div>
                                        <div class="comment-stats">👍 {reply_likes} • 📅 {reply_date}</div>
                                        <div class="comment-text">{reply_text}</div>
                                    </div>
                                    """, unsafe_allow_html=True)

                                    # Interface para gerenciar tags das respostas (igual aos comentários)
                                    if reply_id:
                                        # Buscar todas as tags existentes
                                        all_existing_tags = set()
                                        for c in comments:
                                            c_id = c[2] if len(c) > 2 else None
                                            if c_id:
                                                c_tags = db.get_comment_tags(c_id)
                                                all_existing_tags.update(c_tags)

                                        tag_rules = db.get_tag_rules()
                                        for rule in tag_rules:
                                            all_existing_tags.add(rule['tag_name'])

                                        available_reply_tags = sorted(list(all_existing_tags - set(reply_tags)))

                                        # Layout compacto idêntico aos comentários: Dropdown menor + Adicionar + Lixeira + Tags com X
                                        reply_cols = st.columns([1.5, 0.5, 0.5] + [1] * len(reply_tags))

                                        # Dropdown compacto (sempre visível)
                                        with reply_cols[0]:
                                            selected_reply_tag = st.selectbox(
                                                "Tag",
                                                [""] + available_reply_tags,
                                                key=f"select_reply_tag_{reply_id}",
                                                label_visibility="collapsed"
                                            )

                                        # Botão adicionar
                                        with reply_cols[1]:
                                            if st.button("➕", key=f"add_reply_tag_{reply_id}", help="Adicionar tag"):
                                                if selected_reply_tag:
                                                    if db.add_manual_tag_to_comment(reply_id, selected_reply_tag):
                                                        st.rerun()

                                        # Lixeira ao lado do adicionar
                                        with reply_cols[2]:
                                            if st.button("🗑️", key=f"delete_reply_{reply_id}", help="Excluir resposta"):
                                                if db.delete_comment(reply_id):
                                                    st.success("Excluído!")
                                                    st.rerun()

                                        # Tags atuais com botão X individual
                                        for i, tag in enumerate(reply_tags):
                                            with reply_cols[3 + i]:
                                                if st.button(f"❌ {tag}", key=f"remove_reply_tag_{reply_id}_{tag}", help=f"Remover '{tag}'"):
                                                    if db.remove_tag_from_comment(reply_id, tag):
                                                        st.rerun()
                    except Exception as e:
                        st.warning(f"Erro ao mostrar respostas do comentário {i+1}: {e}")

                except Exception as e:
                    st.error(f"Erro ao exibir comentário {i+1}: {e}")
                    st.write(f"Dados do comentário: {comment}")
        else:
            st.info("📭 Nenhum comentário para exibir com os filtros atuais")

        # Navegação de páginas (se necessário)
        if comments_per_page != "Todos" and total_pages > 1:
            st.markdown("---")
            col1, col2, col3, col4, col5 = st.columns(5)

            with col1:
                if current_page > 1:
                    if st.button("⬅️ Anterior", key=f"prev_page_{video_id}"):
                        st.session_state[f"page_number_{video_id}"] = current_page - 1
                        st.rerun()

            with col3:
                st.markdown(f"**Página {current_page} de {total_pages}**")

            with col5:
                if current_page < total_pages:
                    if st.button("Próxima ➡️", key=f"next_page_{video_id}"):
                        st.session_state[f"page_number_{video_id}"] = current_page + 1
                        st.rerun()

    with tab4:

        col1, col2 = st.columns([2, 1])

        with col1:
            tag_name = st.text_input("Nome da tag:", placeholder="Ex: Comentários de pais de crianças autistas", key=f"tag_name_history_{video_id}")
            terms_input = st.text_area(
                "Termos (um por linha):",
                placeholder="filho\nfilha\nmeu pequeno\nminha pequena\ncriança\nbebê",
                help="Digite um termo por linha. A busca não diferencia maiúsculas/minúsculas.",
                key=f"terms_input_history_{video_id}"
            )

        with col2:
            st.markdown("#### 📋 Exemplo:")
            st.markdown("""
            **Nome:** Comentários de pais

            **Termos:**
            - filho, filha
            - meu pequeno
            - minha pequena
            - criança, bebê
            """)

        if st.button("✅ Criar Regra de Tag", type="primary", key=f"create_tag_history_{video_id}"):
            if tag_name and terms_input:
                terms = [term.strip() for term in terms_input.split('\n') if term.strip()]
                if terms:
                    if db.add_tag_rule(tag_name, terms):
                        st.success(f"✅ Regra de tag '{tag_name}' criada com {len(terms)} termos!")
                        # Aplicar imediatamente ao vídeo atual
                        tags_applied = db.apply_tags_to_comments(video_id)
                        if tags_applied > 0:
                            st.info(f"🏷️ {tags_applied} tags aplicadas a este vídeo!")
                    else:
                        st.error("❌ Erro ao criar regra de tag")
                else:
                    st.warning("⚠️ Adicione pelo menos um termo")
            else:
                st.warning("⚠️ Preencha o nome da tag e os termos")

        st.markdown("---")

        # Botão para aplicar tags existentes
        col1, col2 = st.columns(2)

        with col1:
            if st.button("🏷️ Aplicar Todas as Tags Existentes", type="secondary", key=f"apply_tags_history_{video_id}"):
                tags_applied = db.apply_tags_to_comments(video_id)
                if tags_applied > 0:
                    st.success(f"✅ {tags_applied} tags aplicadas!")
                else:
                    st.info("ℹ️ Nenhuma nova tag foi aplicada")

        with col2:
            # Mostrar estatísticas de tags
            rules = db.get_tag_rules()
            st.metric("📋 Regras de Tags", len(rules))

        # Seção para visualizar regras existentes
        if rules:
            st.markdown("### 📋 Regras de Tags Existentes")

            for rule in rules:
                with st.expander(f"🏷️ {rule['tag_name']} ({len(rule['terms'])} termos)"):
                    col1, col2 = st.columns([3, 1])

                    with col1:
                        st.write("**Termos:**")
                        terms_text = ", ".join(rule['terms'])
                        st.write(terms_text)
                        st.caption(f"Criada em: {rule['created_at']}")

                    with col2:
                        if st.button("🗑️ Excluir", key=f"delete_rule_history_{rule['id']}_{video_id}", type="secondary"):
                            if db.delete_tag_rule(rule['id']):
                                st.success("Regra excluída!")
                                st.rerun()
                            else:
                                st.error("Erro ao excluir regra")

    with tab5:
        st.subheader("📤 Exportar Dados")

        # Exportar para LLM
        col1, col2 = st.columns(2)

        with col1:
            if st.button("📋 Exportar para LLM", type="primary", key=f"export_llm_{video_id}"):
                llm_data = db.export_for_llm(video_id)
                if llm_data:
                    st.download_button(
                        "⬇️ Download JSON para LLM",
                        data=json.dumps(llm_data, indent=2, ensure_ascii=False),
                        file_name=f"llm_data_{video_id}.json",
                        mime="application/json",
                        key=f"download_llm_{video_id}"
                    )
                    st.success("✅ Dados preparados para LLM!")

        with col2:
            if st.button("📊 Exportar Dados Completos", key=f"export_complete_{video_id}"):
                # Exportar todos os dados
                complete_data = {
                    'video_info': {
                        'video_id': video_info[1],
                        'title': video_info[2],
                        'description': video_info[3],
                        'channel_name': video_info[4],
                        'channel_id': video_info[5],
                        'duration': video_info[7],
                        'view_count': video_info[8],
                        'like_count': video_info[9],
                        'upload_date': video_info[11],
                        'tags': json.loads(video_info[12] or '[]'),
                        'categories': json.loads(video_info[13] or '[]'),
                        'language': video_info[16],
                        'extracted_at': video_info[17]
                    },
                    'comments': [
                        {
                            'author': c[3],
                            'text': c[5],
                            'like_count': c[6],
                            'reply_count': c[7],
                            'published_at': c[8],
                            'is_reply': c[10]
                        }
                        for c in comments
                    ],
                    'transcript': [
                        {
                            'text': t[3],
                            'start_time': t[4],
                            'duration': t[5],
                            'confidence': t[6],
                            'language': t[2]
                        }
                        for t in transcript
                    ]
                }

                st.download_button(
                    "⬇️ Download Dados Completos",
                    data=json.dumps(complete_data, indent=2, ensure_ascii=False),
                    file_name=f"complete_data_{video_id}.json",
                    mime="application/json",
                    key=f"download_complete_{video_id}"
                )

        # Preview dos dados para LLM
        if st.checkbox("👁️ Preview dos dados para LLM", key=f"preview_llm_{video_id}"):
            llm_data = db.export_for_llm(video_id)
            if llm_data:
                st.json(llm_data)

def main():
    # Header
    st.markdown('<h1 class="main-header">🎬 YouTube Data Extractor</h1>', unsafe_allow_html=True)

    # Inicializar session state
    if 'viewing_video' not in st.session_state:
        st.session_state.viewing_video = None
    if 'selected_video_id' not in st.session_state:
        st.session_state.selected_video_id = None

    # Inicializar componentes
    db, extractor = init_components()
    

    
    # Área principal
    tab1, tab2, tab3 = st.tabs(["🎬 EXTRAIR", "📚 HISTÓRICO", "🔍 ANÁLISE"])
    
    with tab1:
        
        # Input da URL
        url = st.text_input(
            "🔗 URL do vídeo:",
            placeholder="https://www.youtube.com/watch?v=...",
            help="Cole aqui a URL completa do vídeo do YouTube"
        )

        # Configurações
        col_config1, col_config2 = st.columns(2)

        with col_config1:
            max_comments = st.selectbox(
                "Máximo de comentários:",
                [100, 500, 1000, 2000, -1],
                index=2,
                format_func=lambda x: "Todos" if x == -1 else f"{x:,}",
                help="Escolha quantos comentários extrair (-1 = todos)"
            )

        with col_config2:
            auto_save = st.checkbox("💾 Salvar automaticamente", value=True, help="Salvar dados no banco automaticamente")

        col1, col2, col3 = st.columns([2, 1, 1])
        
        with col1:
            extract_btn = st.button("🚀 EXTRAIR DADOS", type="primary", use_container_width=True)
        
        with col2:
            if url:
                video_id = extractor.extract_video_id(url)
                if video_id:
                    st.success(f"✅ ID: {video_id}")
                else:
                    st.error("❌ URL inválida")
        
        # Processo de extração
        if extract_btn and url:
            if not extractor.extract_video_id(url):
                st.error("❌ URL do YouTube inválida!")
                return
            
            # Container para progresso
            progress_container = st.container()
            
            with progress_container:
                # Barra de progresso
                progress_bar = st.progress(0)
                status_text = st.empty()
                
                try:
                    # Iniciar extração
                    status_text.text("🎬 Extraindo informações do vídeo...")
                    progress_bar.progress(20)
                    
                    result = extractor.extract_all_data(url, max_comments)
                    
                    if not result['success']:
                        st.error(f"❌ Erro na extração: {result['error']}")
                        return
                    
                    progress_bar.progress(60)
                    status_text.text("💾 Salvando no banco de dados...")
                    
                    # Salvar no banco
                    video_info = result['video_info']
                    comments = result['comments']
                    transcript = result['transcript']

                    # Salvar dados
                    db.save_video(video_info)
                    db.save_comments(video_info['video_id'], comments)
                    if transcript:
                        db.save_transcript(video_info['video_id'], transcript)

                    progress_bar.progress(80)
                    status_text.text("🏷️ Aplicando tags aos comentários...")

                    # Aplicar tags automaticamente
                    tags_applied = db.apply_tags_to_comments(video_info['video_id'])

                    progress_bar.progress(100)
                    status_text.text("✅ Extração concluída!")
                    
                    # Mostrar resultados
                    st.balloons()
                    
                    # Dados do vídeo
                    video_info = result['video_info']
                    
                    st.markdown('<div class="success-message">', unsafe_allow_html=True)
                    st.success(f"✅ **{video_info['title']}** extraído com sucesso!")
                    if tags_applied > 0:
                        st.info(f"🏷️ {tags_applied} tags aplicadas automaticamente aos comentários!")
                    st.markdown('</div>', unsafe_allow_html=True)
                    
                    # Métricas
                    col1, col2, col3, col4 = st.columns(4)
                    
                    with col1:
                        st.metric("👁️ Visualizações", extractor.format_number(video_info['view_count']))
                    
                    with col2:
                        st.metric("👍 Likes", extractor.format_number(video_info['like_count']))
                    
                    with col3:
                        st.metric("💬 Comentários", f"{result['stats']['total_comments']:,}")
                    
                    with col4:
                        st.metric("⏱️ Duração", extractor.format_duration(video_info['duration']))
                    
                    # Abas de resultados
                    result_tab1, result_tab2, result_tab3, result_tab4 = st.tabs(["📋 INFORMAÇÕES", "📝 TRANSCRIÇÃO", "💬 COMENTÁRIOS", "🏷️ TAGS"])
                    
                except Exception as e:
                    st.error(f"❌ Erro durante a extração: {str(e)}")
                    progress_bar.progress(0)
                    status_text.text("❌ Falha na extração")
    
    with tab2:

        videos = db.get_all_videos()

        if videos:
            # Converter para DataFrame
            df = pd.DataFrame(videos)
            df['extracted_at'] = pd.to_datetime(df['extracted_at'])

            # Filtros em uma linha
            col1, col2, col3, col4 = st.columns(4)

            with col1:
                search_term = st.text_input("🔍 Buscar por título/canal", key="history_search")

            with col2:
                min_views = st.number_input("Mínimo de visualizações", min_value=0, value=0, key="history_min_views")

            with col3:
                has_transcript_filter = st.selectbox("Transcrição", ["Todos", "Com transcrição", "Sem transcrição"], key="history_transcript")

            with col4:
                # Botão para limpar filtros
                if st.button("🔄 Limpar Filtros", key="clear_filters"):
                    # Usar del para remover as chaves do session_state
                    keys_to_clear = ["history_search", "history_min_views", "history_transcript"]
                    for key in keys_to_clear:
                        if key in st.session_state:
                            del st.session_state[key]
                    st.rerun()

            # Aplicar filtros
            filtered_df = df.copy()

            if search_term:
                filtered_df = filtered_df[
                    filtered_df['title'].str.contains(search_term, case=False, na=False) |
                    filtered_df['channel_name'].str.contains(search_term, case=False, na=False)
                ]

            if min_views > 0:
                filtered_df = filtered_df[filtered_df['view_count'] >= min_views]

            if has_transcript_filter != "Todos":
                has_transcript = has_transcript_filter == "Com transcrição"
                filtered_df = filtered_df[filtered_df['has_transcript'] == has_transcript]

            # Verificar se está visualizando algum vídeo
            if st.session_state.viewing_video and st.session_state.selected_video_id:
                # Mostrar botão para voltar à lista
                if st.button("⬅️ VOLTAR À LISTA", type="secondary", key="back_to_list"):
                    st.session_state.viewing_video = None
                    st.session_state.selected_video_id = None
                    st.rerun()

                st.markdown("---")
                # Mostrar dados do vídeo
                display_video_data(db, st.session_state.selected_video_id, extractor)

            else:
                # Mostrar tabela de vídeos
                if len(filtered_df) > 0:
                    # Preparar dados para exibição com formatação melhor
                    display_df = filtered_df.copy()

                    # Truncar títulos longos para melhor visualização
                    display_df['titulo_display'] = display_df['title'].apply(
                        lambda x: x[:70] + "..." if len(x) > 70 else x
                    )

                    # Formatar números de forma mais compacta
                    display_df['views_display'] = display_df['view_count'].apply(
                        lambda x: f"{x/1000000:.1f}M" if x >= 1000000 else f"{x/1000:.0f}K" if x >= 1000 else str(x)
                    )

                    display_df['comments_display'] = display_df['total_comments'].apply(
                        lambda x: f"{x/1000:.0f}K" if x >= 1000 else str(x)
                    )

                    # Formatar data de forma mais compacta
                    display_df['data_display'] = display_df['extracted_at'].dt.strftime('%d/%m %H:%M')

                    # Configurar colunas da tabela
                    column_config = {
                        'titulo_display': st.column_config.TextColumn('🎬 Título', width="large"),
                        'channel_name': st.column_config.TextColumn('📺 Canal', width="medium"),
                        'views_display': st.column_config.TextColumn('👁️ Views', width="small"),
                        'comments_display': st.column_config.TextColumn('💬 Coment.', width="small"),
                        'has_transcript': st.column_config.CheckboxColumn('📝', width="small"),
                        'data_display': st.column_config.TextColumn('📅 Data', width="small")
                    }

                    # Mostrar tabela com seleção
                    selected_rows = st.dataframe(
                        display_df[['titulo_display', 'channel_name', 'views_display', 'comments_display', 'has_transcript', 'data_display']],
                        use_container_width=True,
                        column_config=column_config,
                        hide_index=True,
                        on_select="rerun",
                        selection_mode="single-row"
                    )

                    # Verificar se alguma linha foi selecionada
                    if selected_rows.selection.rows:
                        selected_idx = selected_rows.selection.rows[0]
                        selected_video = filtered_df.iloc[selected_idx]
                        selected_video_id = selected_video['video_id']

                        # Automaticamente visualizar o vídeo selecionado
                        st.session_state.viewing_video = True
                        st.session_state.selected_video_id = selected_video_id
                        st.rerun()

                else:
                    st.warning("🔍 Nenhum vídeo encontrado com os filtros aplicados")



        else:
            st.info("📭 Nenhum vídeo extraído ainda. Use a aba 'EXTRAIR' para começar!")

    with tab3:
        st.header("🔍 Análise de Dados")
        st.info("🚧 Funcionalidade de análise com LLM será implementada em breve!")
        
        if videos:
            st.subheader("📊 Estatísticas Gerais")
            
            df = pd.DataFrame(videos)
            
            col1, col2, col3 = st.columns(3)
            
            with col1:
                total_views = df['view_count'].sum()
                st.metric("👁️ Total de Visualizações", extractor.format_number(total_views))
            
            with col2:
                total_comments = df['total_comments'].sum()
                st.metric("💬 Total de Comentários", f"{total_comments:,}")
            
            with col3:
                with_transcript = df['has_transcript'].sum()
                st.metric("📝 Com Transcrição", f"{with_transcript}/{len(df)}")

if __name__ == "__main__":
    main()
