import sqlite3
import json
from datetime import datetime
from typing import List, Dict, Any

class YouTubeDatabase:
    def __init__(self, db_path: str = "youtube_data.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """Inicializa o banco de dados com todas as tabelas necessárias"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Tabela principal de vídeos
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS videos (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                video_id TEXT UNIQUE NOT NULL,
                title TEXT NOT NULL,
                description TEXT,
                channel_name TEXT,
                channel_id TEXT,
                channel_url TEXT,
                duration INTEGER,
                view_count INTEGER,
                like_count INTEGER,
                subscriber_count INTEGER,
                upload_date TEXT,
                tags TEXT,  -- JSON array
                categories TEXT,  -- JSON array
                thumbnail_url TEXT,
                video_url TEXT,
                language TEXT,
                extracted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                total_comments INTEGER DEFAULT 0,
                has_transcript BOOLEAN DEFAULT 0
            )
        ''')
        
        # Tabela de comentários
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS comments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                video_id TEXT NOT NULL,
                comment_id TEXT UNIQUE,
                author TEXT,
                author_channel_id TEXT,
                text TEXT NOT NULL,
                like_count INTEGER DEFAULT 0,
                reply_count INTEGER DEFAULT 0,
                published_at TEXT,
                updated_at TEXT,
                is_reply BOOLEAN DEFAULT 0,
                parent_comment_id TEXT,
                sentiment_score REAL,  -- Para análise futura
                FOREIGN KEY (video_id) REFERENCES videos (video_id)
            )
        ''')
        
        # Tabela de transcrições
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS transcripts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                video_id TEXT NOT NULL,
                language TEXT,
                text TEXT NOT NULL,
                start_time REAL,
                duration REAL,
                confidence REAL,
                is_auto_generated BOOLEAN DEFAULT 1,
                FOREIGN KEY (video_id) REFERENCES videos (video_id)
            )
        ''')
        
        # Tabela de metadados adicionais
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS video_metadata (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                video_id TEXT NOT NULL,
                metadata_type TEXT,  -- 'chapters', 'annotations', 'cards', etc.
                data TEXT,  -- JSON data
                extracted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (video_id) REFERENCES videos (video_id)
            )
        ''')
        
        # Tabela de análises (para LLM)
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS llm_analyses (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                video_id TEXT NOT NULL,
                analysis_type TEXT,  -- 'sentiment', 'topics', 'summary', etc.
                prompt_used TEXT,
                result TEXT,
                confidence_score REAL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (video_id) REFERENCES videos (video_id)
            )
        ''')

        # Tabela de regras de tags
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS tag_rules (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                tag_name TEXT NOT NULL,
                terms TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # Tabela de tags dos comentários
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS comment_tags (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                comment_id TEXT NOT NULL,
                tag_name TEXT NOT NULL,
                applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (comment_id) REFERENCES comments (comment_id)
            )
        ''')

        conn.commit()
        conn.close()
    
    def save_video(self, video_data: Dict[str, Any]) -> bool:
        """Salva dados do vídeo no banco"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT OR REPLACE INTO videos (
                    video_id, title, description, channel_name, channel_id, channel_url,
                    duration, view_count, like_count, subscriber_count, upload_date,
                    tags, categories, thumbnail_url, video_url, language, total_comments, has_transcript
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                video_data.get('video_id'),
                video_data.get('title'),
                video_data.get('description', ''),
                video_data.get('channel_name'),
                video_data.get('channel_id'),
                video_data.get('channel_url'),
                video_data.get('duration', 0),
                video_data.get('view_count', 0),
                video_data.get('like_count', 0),
                video_data.get('subscriber_count', 0),
                video_data.get('upload_date'),
                json.dumps(video_data.get('tags', [])),
                json.dumps(video_data.get('categories', [])),
                video_data.get('thumbnail_url'),
                video_data.get('video_url'),
                video_data.get('language'),
                video_data.get('total_comments', 0),
                video_data.get('has_transcript', False)
            ))
            
            conn.commit()
            conn.close()
            return True
        except Exception as e:
            print(f"Erro ao salvar vídeo: {e}")
            return False
    
    def save_comments(self, video_id: str, comments: List[Dict[str, Any]]) -> int:
        """Salva comentários no banco"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Limpar comentários existentes
            cursor.execute('DELETE FROM comments WHERE video_id = ?', (video_id,))
            
            saved_count = 0
            for comment in comments:
                cursor.execute('''
                    INSERT OR IGNORE INTO comments (
                        video_id, comment_id, author, author_channel_id, text,
                        like_count, reply_count, published_at, updated_at,
                        is_reply, parent_comment_id
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    video_id,
                    comment.get('comment_id'),
                    comment.get('author'),
                    comment.get('author_channel_id'),
                    comment.get('text'),
                    comment.get('like_count', 0),
                    comment.get('reply_count', 0),
                    comment.get('published_at'),
                    comment.get('updated_at'),
                    comment.get('is_reply', False),
                    comment.get('parent_comment_id')
                ))
                saved_count += 1
            
            conn.commit()
            conn.close()
            return saved_count
        except Exception as e:
            print(f"Erro ao salvar comentários: {e}")
            return 0
    
    def save_transcript(self, video_id: str, transcript_data: List[Dict[str, Any]]) -> bool:
        """Salva transcrição no banco"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Limpar transcrição existente
            cursor.execute('DELETE FROM transcripts WHERE video_id = ?', (video_id,))
            
            for segment in transcript_data:
                cursor.execute('''
                    INSERT INTO transcripts (
                        video_id, language, text, start_time, duration, confidence, is_auto_generated
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (
                    video_id,
                    segment.get('language', 'pt'),
                    segment.get('text'),
                    segment.get('start', 0),
                    segment.get('duration', 0),
                    segment.get('confidence', 0),
                    segment.get('is_auto_generated', True)
                ))
            
            conn.commit()
            conn.close()
            return True
        except Exception as e:
            print(f"Erro ao salvar transcrição: {e}")
            return False
    
    def get_video_data(self, video_id: str) -> Dict[str, Any]:
        """Recupera todos os dados de um vídeo para análise LLM"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Dados do vídeo
        cursor.execute('SELECT * FROM videos WHERE video_id = ?', (video_id,))
        video = cursor.fetchone()
        
        if not video:
            return {}
        
        # Comentários
        cursor.execute('SELECT * FROM comments WHERE video_id = ? ORDER BY like_count DESC', (video_id,))
        comments = cursor.fetchall()
        
        # Transcrição
        cursor.execute('SELECT * FROM transcripts WHERE video_id = ? ORDER BY start_time', (video_id,))
        transcript = cursor.fetchall()
        
        conn.close()
        
        return {
            'video': video,
            'comments': comments,
            'transcript': transcript
        }

    def delete_comment(self, comment_id: str) -> bool:
        """Exclui um comentário e todas as suas respostas"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            # Primeiro, excluir todas as respostas do comentário
            cursor.execute('DELETE FROM comment_tags WHERE comment_id IN (SELECT comment_id FROM comments WHERE parent_comment_id = ?)', (comment_id,))
            cursor.execute('DELETE FROM comments WHERE parent_comment_id = ?', (comment_id,))

            # Depois, excluir as tags do comentário principal
            cursor.execute('DELETE FROM comment_tags WHERE comment_id = ?', (comment_id,))

            # Por fim, excluir o comentário principal
            cursor.execute('DELETE FROM comments WHERE comment_id = ?', (comment_id,))

            conn.commit()
            return True
        except Exception as e:
            print(f"Erro ao excluir comentário: {e}")
            return False
        finally:
            conn.close()

    def add_tag_rule(self, tag_name: str, terms: List[str]) -> bool:
        """Adiciona uma nova regra de tag"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            terms_str = json.dumps(terms)
            cursor.execute('INSERT INTO tag_rules (tag_name, terms) VALUES (?, ?)', (tag_name, terms_str))
            conn.commit()
            return True
        except Exception as e:
            print(f"Erro ao adicionar regra de tag: {e}")
            return False
        finally:
            conn.close()

    def get_tag_rules(self) -> List[Dict[str, Any]]:
        """Recupera todas as regras de tags"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('SELECT id, tag_name, terms, created_at FROM tag_rules ORDER BY tag_name')
        rules = cursor.fetchall()

        conn.close()

        return [
            {
                'id': rule[0],
                'tag_name': rule[1],
                'terms': json.loads(rule[2]),
                'created_at': rule[3]
            }
            for rule in rules
        ]

    def apply_tags_to_comments(self, video_id: str) -> int:
        """Aplica todas as regras de tags aos comentários de um vídeo"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # Buscar todas as regras de tags
        rules = self.get_tag_rules()

        # Buscar todos os comentários do vídeo
        cursor.execute('SELECT comment_id, text FROM comments WHERE video_id = ?', (video_id,))
        comments = cursor.fetchall()

        tags_applied = 0

        for comment_id, text in comments:
            text_lower = text.lower()

            for rule in rules:
                # Verificar se algum termo da regra está no comentário
                for term in rule['terms']:
                    if term.lower() in text_lower:
                        # Verificar se a tag já não foi aplicada
                        cursor.execute('SELECT id FROM comment_tags WHERE comment_id = ? AND tag_name = ?',
                                     (comment_id, rule['tag_name']))
                        if not cursor.fetchone():
                            # Aplicar a tag
                            cursor.execute('INSERT INTO comment_tags (comment_id, tag_name) VALUES (?, ?)',
                                         (comment_id, rule['tag_name']))
                            tags_applied += 1
                        break  # Não precisa verificar outros termos da mesma regra

        conn.commit()
        conn.close()

        return tags_applied

    def get_comment_tags(self, comment_id: str) -> List[str]:
        """Recupera todas as tags de um comentário"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('SELECT tag_name FROM comment_tags WHERE comment_id = ? ORDER BY tag_name', (comment_id,))
        tags = cursor.fetchall()

        conn.close()

        return [tag[0] for tag in tags]

    def delete_tag_rule(self, rule_id: int) -> bool:
        """Exclui uma regra de tag e remove todas as tags aplicadas por ela"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            # Buscar o nome da tag antes de excluir
            cursor.execute('SELECT tag_name FROM tag_rules WHERE id = ?', (rule_id,))
            result = cursor.fetchone()
            if not result:
                return False

            tag_name = result[0]

            # Remover todas as tags aplicadas
            cursor.execute('DELETE FROM comment_tags WHERE tag_name = ?', (tag_name,))

            # Remover a regra
            cursor.execute('DELETE FROM tag_rules WHERE id = ?', (rule_id,))

            conn.commit()
            return True
        except Exception as e:
            print(f"Erro ao excluir regra de tag: {e}")
            return False
        finally:
            conn.close()
    
    def get_all_videos(self) -> List[Dict[str, Any]]:
        """Lista todos os vídeos extraídos"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT video_id, title, channel_name, view_count, total_comments, 
                   has_transcript, extracted_at 
            FROM videos 
            ORDER BY extracted_at DESC
        ''')
        
        videos = cursor.fetchall()
        conn.close()
        
        columns = ['video_id', 'title', 'channel_name', 'view_count', 'total_comments', 'has_transcript', 'extracted_at']
        return [dict(zip(columns, video)) for video in videos]
    
    def export_for_llm(self, video_id: str) -> Dict[str, Any]:
        """Exporta dados estruturados para análise LLM"""
        data = self.get_video_data(video_id)
        
        if not data:
            return {}
        
        # Estruturar dados para LLM
        video_info = data['video']
        
        # Transcrição completa
        full_transcript = " ".join([segment[3] for segment in data['transcript']])
        
        # Top comentários
        top_comments = [
            {
                'author': comment[3],
                'text': comment[5],
                'likes': comment[6],
                'replies': comment[7]
            }
            for comment in data['comments'][:50]  # Top 50 comentários
        ]
        
        return {
            'video_info': {
                'title': video_info[2],
                'description': video_info[3],
                'channel': video_info[4],
                'duration': video_info[7],
                'views': video_info[8],
                'likes': video_info[9],
                'upload_date': video_info[11],
                'tags': json.loads(video_info[12] or '[]'),
                'categories': json.loads(video_info[13] or '[]')
            },
            'transcript': full_transcript,
            'top_comments': top_comments,
            'stats': {
                'total_comments': len(data['comments']),
                'has_transcript': bool(data['transcript']),
                'transcript_length': len(full_transcript.split()) if full_transcript else 0
            }
        }
