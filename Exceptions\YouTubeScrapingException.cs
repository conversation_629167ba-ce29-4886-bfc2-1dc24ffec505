using System;

namespace YouTubeCreator.Exceptions
{
    public class YouTubeScrapingException : Exception
    {
        public string? VideoId { get; }
        public string? VideoUrl { get; }

        public YouTubeScrapingException(string message) : base(message)
        {
        }

        public YouTubeScrapingException(string message, Exception innerException) : base(message, innerException)
        {
        }

        public YouTubeScrapingException(string message, string videoId, string videoUrl) : base(message)
        {
            VideoId = videoId;
            VideoUrl = videoUrl;
        }

        public YouTubeScrapingException(string message, string videoId, string videoUrl, Exception innerException) 
            : base(message, innerException)
        {
            VideoId = videoId;
            VideoUrl = videoUrl;
        }
    }

    public class InvalidVideoUrlException : YouTubeScrapingException
    {
        public InvalidVideoUrlException(string url) 
            : base($"URL do vídeo inválida: {url}")
        {
        }
    }

    public class VideoNotAccessibleException : YouTubeScrapingException
    {
        public VideoNotAccessibleException(string videoId, string reason) 
            : base($"Vídeo {videoId} não está acessível: {reason}", videoId, $"https://www.youtube.com/watch?v={videoId}")
        {
        }
    }

    public class RateLimitException : YouTubeScrapingException
    {
        public RateLimitException() 
            : base("Limite de requisições atingido. Tente novamente em alguns minutos.")
        {
        }
    }
}
