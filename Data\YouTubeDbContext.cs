using Microsoft.EntityFrameworkCore;
using YouTubeCreator.Models;

namespace YouTubeCreator.Data
{
    public class YouTubeDbContext : DbContext
    {
        public DbSet<VideoRecord> Videos { get; set; }
        public DbSet<CommentRecord> Comments { get; set; }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder.UseSqlite("Data Source=youtube_data.db");
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<VideoRecord>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.VideoId).IsRequired();
                entity.Property(e => e.Title).IsRequired();
                entity.HasIndex(e => e.VideoId).IsUnique();
            });

            modelBuilder.Entity<CommentRecord>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.VideoId).IsRequired();
                entity.Property(e => e.Author).IsRequired();
                entity.Property(e => e.Text).IsRequired();
                
                entity.HasOne<VideoRecord>()
                    .WithMany()
                    .HasForeignKey(e => e.VideoId)
                    .HasPrincipalKey(e => e.VideoId);
            });
        }
    }

    public class VideoRecord
    {
        public int Id { get; set; }
        public string VideoId { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string ChannelName { get; set; } = string.Empty;
        public string ChannelId { get; set; } = string.Empty;
        public long ViewCount { get; set; }
        public long LikeCount { get; set; }
        public DateTime PublishDate { get; set; }
        public TimeSpan Duration { get; set; }
        public string Tags { get; set; } = string.Empty; // JSON string
        public string ThumbnailUrl { get; set; } = string.Empty;
        public string VideoUrl { get; set; } = string.Empty;
        public string Transcript { get; set; } = string.Empty;
        public DateTime ExtractedAt { get; set; }
        public int TotalComments { get; set; }
    }

    public class CommentRecord
    {
        public int Id { get; set; }
        public string VideoId { get; set; } = string.Empty;
        public string Author { get; set; } = string.Empty;
        public string AuthorChannelId { get; set; } = string.Empty;
        public string Text { get; set; } = string.Empty;
        public long LikeCount { get; set; }
        public DateTime PublishDate { get; set; }
        public bool IsReply { get; set; }
        public int? ParentCommentId { get; set; }
    }
}
