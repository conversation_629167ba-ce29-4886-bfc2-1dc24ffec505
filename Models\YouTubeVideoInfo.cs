using System;
using System.Collections.Generic;

namespace YouTubeCreator.Models
{
    public class YouTubeVideoInfo
    {
        public string VideoId { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string ChannelName { get; set; } = string.Empty;
        public string ChannelId { get; set; } = string.Empty;
        public long ViewCount { get; set; }
        public long LikeCount { get; set; }
        public DateTime PublishDate { get; set; }
        public TimeSpan Duration { get; set; }
        public List<string> Tags { get; set; } = new List<string>();
        public string ThumbnailUrl { get; set; } = string.Empty;
        public List<YouTubeComment> Comments { get; set; } = new List<YouTubeComment>();
        public string VideoUrl { get; set; } = string.Empty;
        
        public override string ToString()
        {
            return $"Título: {Title}\n" +
                   $"Canal: {ChannelName}\n" +
                   $"Visualizações: {ViewCount:N0}\n" +
                   $"Likes: {LikeCount:N0}\n" +
                   $"Data de Publicação: {PublishDate:dd/MM/yyyy}\n" +
                   $"Duração: {Duration}\n" +
                   $"Tags: {string.Join(", ", Tags)}\n" +
                   $"Comentários: {Comments.Count}";
        }
    }

    public class YouTubeComment
    {
        public string Author { get; set; } = string.Empty;
        public string AuthorChannelId { get; set; } = string.Empty;
        public string Text { get; set; } = string.Empty;
        public long LikeCount { get; set; }
        public DateTime PublishDate { get; set; }
        public List<YouTubeComment> Replies { get; set; } = new List<YouTubeComment>();
        public bool IsReply { get; set; }
        
        public override string ToString()
        {
            var indent = IsReply ? "  └─ " : "";
            return $"{indent}{Author}: {Text} ({LikeCount} likes)";
        }
    }
}
