using System.Collections.ObjectModel;
using System.IO;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using Microsoft.EntityFrameworkCore;
using Microsoft.Win32;
using YouTubeCreator.Data;
using Newtonsoft.Json;

namespace YouTubeCreator
{
    public partial class HistoryWindow : Window
    {
        private YouTubeDbContext? _dbContext;
        private ObservableCollection<VideoRecord>? _videos;
        private ObservableCollection<VideoRecord>? _filteredVideos;

        public event Action<VideoRecord>? VideoSelected;

        public HistoryWindow(YouTubeDbContext dbContext)
        {
            InitializeComponent();
            _dbContext = dbContext;
            _videos = new ObservableCollection<VideoRecord>();
            _filteredVideos = new ObservableCollection<VideoRecord>();
            
            VideosListView.ItemsSource = _filteredVideos;
            VideosListView.SelectionChanged += VideosListView_SelectionChanged;
            
            LoadVideos();
        }

        private async void LoadVideos()
        {
            try
            {
                if (_dbContext != null)
                {
                    var videos = await _dbContext.Videos
                        .OrderByDescending(v => v.ExtractedAt)
                        .ToListAsync();

                    _videos.Clear();
                    foreach (var video in videos)
                    {
                        _videos.Add(video);
                    }

                    FilterVideos("");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erro ao carregar vídeos: {ex.Message}", "Erro", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void FilterVideos(string searchText)
        {
            if (_filteredVideos != null)
                _filteredVideos.Clear();
            
            var filtered = string.IsNullOrEmpty(searchText) 
                ? _videos 
                : _videos.Where(v => v.Title.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                                    v.ChannelName.Contains(searchText, StringComparison.OrdinalIgnoreCase));
            
            if (_filteredVideos != null)
                foreach (var video in filtered)
                {
                    _filteredVideos.Add(video);
                }
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            var searchText = SearchTextBox.Text;
            FilterVideos(searchText);
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            LoadVideos();
        }

        private void VideosListView_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            var hasSelection = VideosListView.SelectedItem != null;
            SelectButton.IsEnabled = hasSelection;
            DeleteButton.IsEnabled = hasSelection;
            ExportButton.IsEnabled = hasSelection;
        }

        private void VideosListView_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            if (VideosListView.SelectedItem is VideoRecord selectedVideo)
            {
                VideoSelected?.Invoke(selectedVideo);
                Close();
            }
        }

        private void SelectButton_Click(object sender, RoutedEventArgs e)
        {
            if (VideosListView.SelectedItem is VideoRecord selectedVideo)
            {
                VideoSelected?.Invoke(selectedVideo);
                Close();
            }
        }

        private async void DeleteButton_Click(object sender, RoutedEventArgs e)
        {
            if (VideosListView.SelectedItem is VideoRecord selectedVideo)
            {
                var result = MessageBox.Show(
                    $"Tem certeza que deseja excluir o vídeo '{selectedVideo.Title}' e todos os seus comentários?",
                    "Confirmar Exclusão",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        // Remover comentários
                        var comments = await _dbContext.Comments
                            .Where(c => c.VideoId == selectedVideo.VideoId)
                            .ToListAsync();
                        _dbContext.Comments.RemoveRange(comments);

                        // Remover vídeo
                        _dbContext.Videos.Remove(selectedVideo);
                        
                        await _dbContext.SaveChangesAsync();
                        
                        // Atualizar lista
                        _videos.Remove(selectedVideo);
                        _filteredVideos.Remove(selectedVideo);

                        MessageBox.Show("Vídeo excluído com sucesso!", "Sucesso", 
                            MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"Erro ao excluir vídeo: {ex.Message}", "Erro", 
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }

        private async void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            if (VideosListView.SelectedItem is VideoRecord selectedVideo)
            {
                try
                {
                    var saveDialog = new SaveFileDialog
                    {
                        Filter = "JSON files (*.json)|*.json|All files (*.*)|*.*",
                        FileName = $"video_data_{selectedVideo.VideoId}_{DateTime.Now:yyyyMMdd_HHmmss}.json"
                    };

                    if (saveDialog.ShowDialog() == true)
                    {
                        // Carregar comentários
                        var comments = await _dbContext.Comments
                            .Where(c => c.VideoId == selectedVideo.VideoId)
                            .OrderByDescending(c => c.LikeCount)
                            .ToListAsync();

                        var tags = JsonConvert.DeserializeObject<List<string>>(selectedVideo.Tags) ?? new List<string>();

                        // Criar objeto estruturado para LLM
                        var dataForLLM = new
                        {
                            video_metadata = new
                            {
                                video_id = selectedVideo.VideoId,
                                title = selectedVideo.Title,
                                channel_name = selectedVideo.ChannelName,
                                channel_id = selectedVideo.ChannelId,
                                url = selectedVideo.VideoUrl,
                                view_count = selectedVideo.ViewCount,
                                like_count = selectedVideo.LikeCount,
                                publish_date = selectedVideo.PublishDate.ToString("yyyy-MM-dd HH:mm:ss"),
                                duration = selectedVideo.Duration.ToString(),
                                thumbnail_url = selectedVideo.ThumbnailUrl,
                                tags = tags
                            },
                            content = new
                            {
                                description = selectedVideo.Description.Replace("=== TRANSCRIÇÃO ===", "").Trim(),
                                transcript = selectedVideo.Transcript,
                                comments = comments.Select(c => new
                                {
                                    author = c.Author,
                                    author_channel_id = c.AuthorChannelId,
                                    text = c.Text,
                                    like_count = c.LikeCount,
                                    publish_date = c.PublishDate.ToString("yyyy-MM-dd HH:mm:ss"),
                                    is_reply = c.IsReply
                                }).ToList()
                            },
                            extraction_metadata = new
                            {
                                extracted_at = selectedVideo.ExtractedAt.ToString("yyyy-MM-dd HH:mm:ss"),
                                total_comments = selectedVideo.TotalComments,
                                extraction_tool = "YouTubeCreator with YoutubeExplode + yt-dlp"
                            }
                        };

                        var jsonContent = JsonConvert.SerializeObject(dataForLLM, Formatting.Indented);
                        await File.WriteAllTextAsync(saveDialog.FileName, jsonContent);

                        MessageBox.Show($"Dados exportados com sucesso para:\n{saveDialog.FileName}", 
                            "Exportação Concluída", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Erro ao exportar dados: {ex.Message}", "Erro", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }
    }
}
