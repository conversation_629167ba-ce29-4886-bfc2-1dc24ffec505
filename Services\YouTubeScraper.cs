using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using YoutubeExplode;
using YoutubeExplode.Videos;
using YoutubeExplode.Channels;
using YouTubeCreator.Models;
using YouTubeCreator.Exceptions;

namespace YouTubeCreator.Services
{
    public class YouTubeScraper : IDisposable
    {
        private readonly YoutubeClient _youtubeClient;

        public YouTubeScraper()
        {
            _youtubeClient = new YoutubeClient();
        }

        public async Task<YouTubeVideoInfo> ExtractVideoInfoAsync(string videoUrl)
        {
            if (string.IsNullOrWhiteSpace(videoUrl))
                throw new InvalidVideoUrlException(videoUrl ?? "null");

            try
            {
                // A YoutubeExplode aceita URLs diretamente, não precisa extrair o ID manualmente
                var video = await _youtubeClient.Videos.GetAsync(videoUrl);
                var channel = await _youtubeClient.Channels.GetAsync(video.Author.ChannelId);

                var videoInfo = new YouTubeVideoInfo
                {
                    VideoId = video.Id.Value,
                    VideoUrl = video.Url,
                    Title = video.Title,
                    Description = video.Description,
                    ChannelName = video.Author.ChannelTitle,
                    ChannelId = video.Author.ChannelId.Value,
                    ViewCount = video.Engagement.ViewCount,
                    LikeCount = video.Engagement.LikeCount,
                    PublishDate = video.UploadDate.DateTime,
                    Duration = video.Duration ?? TimeSpan.Zero,
                    ThumbnailUrl = video.Thumbnails.OrderByDescending(t => t.Resolution.Area).FirstOrDefault()?.Url ?? "",
                    Tags = video.Keywords.ToList()
                };

                // Tentar extrair transcrições usando YoutubeExplode
                try
                {
                    Console.WriteLine("🔍 Extraindo transcrições...");
                    var transcripts = await ExtractTranscriptsAsync(videoUrl);
                    if (!string.IsNullOrEmpty(transcripts))
                    {
                        videoInfo.Description += "\n\n=== TRANSCRIÇÃO ===\n" + transcripts;
                        Console.WriteLine("✅ Transcrição extraída com sucesso!");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"⚠️  Não foi possível extrair transcrição: {ex.Message}");
                }

                return videoInfo;
            }
            catch (YoutubeExplode.Exceptions.VideoUnavailableException)
            {
                throw new VideoNotAccessibleException("", "Vídeo indisponível");
            }
            catch (ArgumentException)
            {
                throw new InvalidVideoUrlException(videoUrl);
            }
            catch (Exception ex)
            {
                throw new YouTubeScrapingException($"Erro ao extrair informações do vídeo: {ex.Message}", "", videoUrl, ex);
            }
        }

        public async Task<string> ExtractTranscriptsAsync(string videoUrl)
        {
            try
            {
                var trackManifest = await _youtubeClient.Videos.ClosedCaptions.GetManifestAsync(videoUrl);

                if (trackManifest.Tracks.Count == 0)
                {
                    return "";
                }

                // Priorizar português, depois inglês, depois qualquer idioma
                var track = trackManifest.Tracks.FirstOrDefault(t => t.Language.Code.StartsWith("pt")) ??
                           trackManifest.Tracks.FirstOrDefault(t => t.Language.Code.StartsWith("en")) ??
                           trackManifest.Tracks.FirstOrDefault();

                if (track == null)
                {
                    return "";
                }

                var closedCaptions = await _youtubeClient.Videos.ClosedCaptions.GetAsync(track);

                var transcript = string.Join("\n", closedCaptions.Captions.Select(c =>
                    $"[{c.Offset:mm\\:ss}] {c.Text}"));

                return transcript;
            }
            catch (Exception)
            {
                return "";
            }
        }

        public async Task<List<YouTubeComment>> ExtractCommentsAsync(string videoUrl, int maxComments = -1)
        {
            var comments = new List<YouTubeComment>();

            try
            {
                if (maxComments == -1)
                {
                    Console.WriteLine("🔍 Extraindo TODOS os comentários usando yt-dlp...");
                }
                else
                {
                    Console.WriteLine($"🔍 Extraindo comentários usando yt-dlp... (máximo: {maxComments})");
                }

                // Tentar usar yt-dlp se estiver disponível
                var ytDlpComments = await TryExtractCommentsWithYtDlp(videoUrl, maxComments);
                if (ytDlpComments.Count > 0)
                {
                    return ytDlpComments;
                }

                Console.WriteLine("⚠️  yt-dlp não encontrado ou não funcionou.");
                Console.WriteLine("💡 Para extrair comentários completos, instale yt-dlp:");
                Console.WriteLine("   pip install yt-dlp");
                Console.WriteLine("   ou baixe de: https://github.com/yt-dlp/yt-dlp");

                return comments;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Erro ao extrair comentários: {ex.Message}");
                return comments;
            }
        }

        private async Task<List<YouTubeComment>> TryExtractCommentsWithYtDlp(string videoUrl, int maxComments)
        {
            var comments = new List<YouTubeComment>();
            var tempDir = Path.GetTempPath();
            var videoId = ExtractVideoIdFromUrl(videoUrl);
            var commentsFile = Path.Combine(tempDir, $"{videoId}.info.json");

            try
            {
                // Limpar arquivo anterior se existir
                if (File.Exists(commentsFile))
                    File.Delete(commentsFile);

                // Comando para extrair TODOS os dados: comentários, transcrições, metadados
                var arguments = $"-m yt_dlp --write-info-json --write-comments --write-subs --write-auto-subs --skip-download";

                // Se maxComments não for -1, limitar comentários
                if (maxComments > 0)
                {
                    arguments += $" --max-comments {maxComments}";
                }

                arguments += $" --output \"{Path.Combine(tempDir, "%(id)s.%(ext)s")}\" \"{videoUrl}\"";

                var processInfo = new System.Diagnostics.ProcessStartInfo
                {
                    FileName = "python",
                    Arguments = arguments,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    UseShellExecute = false,
                    CreateNoWindow = true
                };

                using var process = System.Diagnostics.Process.Start(processInfo);
                if (process == null)
                    return comments;

                var output = await process.StandardOutput.ReadToEndAsync();
                var error = await process.StandardError.ReadToEndAsync();

                await process.WaitForExitAsync();

                if (process.ExitCode == 0)
                {
                    Console.WriteLine("✅ yt-dlp executado com sucesso!");

                    // Aguardar um pouco para garantir que o arquivo foi escrito
                    await Task.Delay(1000);

                    // Tentar ler o arquivo de comentários
                    if (File.Exists(commentsFile))
                    {
                        var jsonContent = await File.ReadAllTextAsync(commentsFile);
                        comments = ParseCommentsFromJson(jsonContent, maxComments);

                        // Limpar arquivo temporário
                        File.Delete(commentsFile);

                        Console.WriteLine($"📝 {comments.Count} comentários extraídos com sucesso!");

                        // Verificar se há arquivos de transcrição
                        CheckForTranscriptionFiles(tempDir, videoId);
                    }
                    else
                    {
                        Console.WriteLine("⚠️  Arquivo de comentários não foi criado.");
                    }

                    // Delay para evitar rate limiting
                    Console.WriteLine("⏳ Aguardando para evitar rate limiting...");
                    await Task.Delay(2000);
                }
                else
                {
                    Console.WriteLine($"⚠️  yt-dlp falhou: {error}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️  Não foi possível executar yt-dlp: {ex.Message}");
            }

            return comments;
        }

        private string ExtractVideoIdFromUrl(string url)
        {
            var patterns = new[]
            {
                @"(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([a-zA-Z0-9_-]{11})",
                @"youtube\.com\/watch\?.*v=([a-zA-Z0-9_-]{11})"
            };

            foreach (var pattern in patterns)
            {
                var match = Regex.Match(url, pattern);
                if (match.Success)
                    return match.Groups[1].Value;
            }

            return "unknown";
        }

        private Task CheckForTranscriptionFiles(string tempDir, string videoId)
        {
            try
            {
                var transcriptionFiles = Directory.GetFiles(tempDir, $"{videoId}*.vtt")
                    .Concat(Directory.GetFiles(tempDir, $"{videoId}*.srt"))
                    .ToArray();

                if (transcriptionFiles.Length > 0)
                {
                    Console.WriteLine($"📝 Encontrados {transcriptionFiles.Length} arquivos de transcrição!");
                    foreach (var file in transcriptionFiles)
                    {
                        Console.WriteLine($"   - {Path.GetFileName(file)}");
                        // Limpar arquivos de transcrição temporários
                        File.Delete(file);
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️  Erro ao verificar transcrições: {ex.Message}");
            }

            return Task.CompletedTask;
        }

        private List<YouTubeComment> ParseCommentsFromJson(string jsonContent, int maxComments)
        {
            var comments = new List<YouTubeComment>();

            try
            {
                dynamic? videoInfo = Newtonsoft.Json.JsonConvert.DeserializeObject(jsonContent);

                if (videoInfo?.comments != null)
                {
                    int count = 0;
                    foreach (var comment in videoInfo.comments)
                    {
                        if (maxComments > 0 && count >= maxComments) break;

                        var youtubeComment = new YouTubeComment
                        {
                            Author = comment.author?.ToString() ?? "Autor desconhecido",
                            Text = comment.text?.ToString() ?? "",
                            LikeCount = comment.like_count != null ? (long)comment.like_count : 0,
                            PublishDate = DateTime.TryParse(comment.timestamp?.ToString(), out DateTime date) ? date : DateTime.Now,
                            IsReply = comment.parent?.ToString() != "root",
                            AuthorChannelId = comment.author_id?.ToString() ?? ""
                        };

                        comments.Add(youtubeComment);
                        count++;
                    }
                }

                Console.WriteLine($"📊 Total de comentários processados: {comments.Count}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️  Erro ao processar comentários: {ex.Message}");
            }

            return comments;
        }

        public void Dispose()
        {
            // YoutubeClient não precisa de dispose explícito
        }
    }
}
