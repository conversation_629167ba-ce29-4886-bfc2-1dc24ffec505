@echo off
echo.
echo ========================================
echo   🎬 YouTube Data Extractor - Python
echo ========================================
echo.

echo 🐍 Verificando Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python não encontrado!
    echo.
    echo Por favor, instale Python 3.8+ de: https://python.org
    echo.
    pause
    exit /b 1
)

echo ✅ Python encontrado!
echo.

echo 📦 Instalando dependências...
echo.

REM Atualizar pip
python -m pip install --upgrade pip

REM Instalar dependências
pip install -r requirements.txt

echo.
echo ✅ Dependências instaladas!
echo.

echo 🚀 Iniciando aplicação...
echo.
echo 📱 A aplicação abrirá automaticamente no seu navegador
echo 🌐 URL: http://localhost:8501
echo.
echo ⚠️  Para parar a aplicação, pressione Ctrl+C
echo.

REM Executar aplicação
streamlit run app.py

echo.
echo 👋 Aplicação encerrada!
pause
