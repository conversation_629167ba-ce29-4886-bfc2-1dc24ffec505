# 🎬 YouTube Data Extractor

**Extração completa de dados do YouTube para análise com LLM**

## 🎯 Funcionalidades

### ✅ **Extração Completa**
- **📋 Informações do Vídeo:** Título, descrição, canal, visualizações, likes, duração, tags
- **💬 TODOS os Comentários:** Autor, texto, likes, respostas, timestamps
- **📝 Transcrições Completas:** Legendas automáticas e manuais com timestamps
- **📊 Metadados:** Categorias, idioma, data de publicação, thumbnails

### 💾 **Banco de Dados Estruturado**
- **SQLite local** para armazenamento
- **Tabelas organizadas:** videos, comments, transcripts, metadata
- **Dados estruturados** prontos para análise LLM
- **Exportação JSON** otimizada

### 🖥️ **Interface Moderna**
- **Streamlit** - Interface web moderna
- **Tempo real** - Progresso da extração
- **Filtros avançados** - Busca e organização
- **Visualização completa** - Abas organizadas

## 🚀 Instalação e Uso

### **Método 1: Automático (Recomendado)**

1. **Execute o instalador:**
   ```bash
   install_and_run.bat
   ```

2. **Aguarde a instalação** das dependências

3. **A aplicação abrirá** automaticamente no navegador

### **Método 2: Manual**

1. **Instale as dependências:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Execute a aplicação:**
   ```bash
   streamlit run app.py
   ```

3. **Acesse:** http://localhost:8501

## 📱 Como Usar

### 1. **Extrair Dados**
- Cole a URL do YouTube
- Clique em "🚀 EXTRAIR DADOS"
- Aguarde o processo (pode levar alguns minutos)
- Visualize os resultados nas abas

### 2. **Configurações**
- **Máximo de comentários:** 100, 500, 1000, 2000 ou Todos
- **Salvar automaticamente:** Ativa/desativa salvamento no banco
- **Filtros:** Por visualizações, transcrição, etc.

### 3. **Exportar para LLM**
- Selecione um vídeo no histórico
- Clique em "📋 Exportar para LLM"
- Baixe o arquivo JSON estruturado

## 📊 Estrutura dos Dados

### **Vídeo**
```json
{
  "video_info": {
    "title": "Título do vídeo",
    "description": "Descrição completa",
    "channel": "Nome do canal",
    "duration": 300,
    "views": 1000000,
    "likes": 50000,
    "upload_date": "2024-01-01",
    "tags": ["tag1", "tag2"],
    "categories": ["Entertainment"]
  }
}
```

### **Comentários**
```json
{
  "top_comments": [
    {
      "author": "Nome do usuário",
      "text": "Texto do comentário",
      "likes": 100,
      "replies": 5
    }
  ]
}
```

### **Transcrição**
```json
{
  "transcript": "Texto completo da transcrição com timestamps...",
  "stats": {
    "total_comments": 1500,
    "has_transcript": true,
    "transcript_length": 2500
  }
}
```

## 🔧 Arquivos do Projeto

```
📁 youtube-extractor/
├── 📄 app.py                    # Interface principal (Streamlit)
├── 📄 youtube_extractor.py      # Extrator de dados (yt-dlp)
├── 📄 database.py              # Gerenciamento do banco SQLite
├── 📄 requirements.txt         # Dependências Python
├── 📄 install_and_run.bat      # Instalador automático
├── 📄 README.md               # Este arquivo
└── 📄 youtube_data.db         # Banco de dados (criado automaticamente)
```

## 🛠️ Tecnologias

- **Python 3.8+**
- **Streamlit** - Interface web moderna
- **yt-dlp** - Extração robusta do YouTube
- **SQLite** - Banco de dados local
- **youtube-transcript-api** - Transcrições
- **pandas** - Manipulação de dados

## ⚡ Vantagens sobre C#

| Aspecto | C# + WPF | Python + Streamlit |
|---------|----------|-------------------|
| **Extração de comentários** | ❌ Limitada/problemática | ✅ Completa e robusta |
| **Instalação** | ❌ Dependências complexas | ✅ `pip install` simples |
| **Rate limiting** | ❌ Erros 429 frequentes | ✅ Melhor tratamento |
| **Interface** | ⚠️ Código complexo | ✅ 10x mais simples |
| **Manutenção** | ❌ Dependências externas | ✅ Ecossistema Python |
| **Análise de dados** | ⚠️ Limitada | ✅ Ferramentas nativas |

## 🎯 Para Análise LLM

### **Dados Estruturados**
- JSON otimizado para LLMs
- Comentários ordenados por relevância
- Transcrição com timestamps
- Metadados completos

### **Casos de Uso**
- **Análise de sentimento** dos comentários
- **Extração de tópicos** principais
- **Resumo automático** do conteúdo
- **Identificação de trends** e padrões
- **Análise de engajamento**

### **Exemplo de Prompt**
```
Analise os dados do vídeo do YouTube:

Título: {title}
Transcrição: {transcript}
Top 50 comentários: {comments}

Forneça:
1. Resumo do conteúdo (3 parágrafos)
2. Principais tópicos discutidos
3. Sentimento geral dos comentários
4. Insights sobre o engajamento
```

## 🚨 Requisitos

- **Python 3.8+**
- **Conexão com internet**
- **Navegador web moderno**

## 📞 Suporte

Se encontrar problemas:

1. **Verifique se o Python está instalado**
2. **Execute:** `pip install -r requirements.txt`
3. **Teste com vídeos públicos** (não privados/restritos)
4. **Aguarde o processo** - pode levar alguns minutos

---

**🎬 Pronto para extrair dados do YouTube de forma profissional!**
