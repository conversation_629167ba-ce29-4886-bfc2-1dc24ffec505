import yt_dlp
import re
from typing import Dict, List, Any, Optional
from youtube_transcript_api import YouTubeTranscriptApi, TranscriptsDisabled, NoTranscriptFound
from datetime import datetime
import json
import os
import tempfile

class YouTubeExtractor:
    def __init__(self):
        self.ydl_opts = {
            'quiet': True,
            'no_warnings': True,
            'extract_flat': False,
            'writeinfojson': False,
            'writesubtitles': False,
            'writeautomaticsub': False,
            'getcomments': True,
            'ignoreerrors': True,
        }
    
    def extract_video_id(self, url: str) -> Optional[str]:
        """Extrai o ID do vídeo da URL"""
        patterns = [
            r'(?:youtube\.com/watch\?v=|youtu\.be/|youtube\.com/embed/)([^&\n?#]+)',
            r'youtube\.com/watch\?.*v=([^&\n?#]+)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                return match.group(1)
        return None
    
    def extract_video_info(self, url: str) -> Optional[Dict[str, Any]]:
        """Extrai informações básicas do vídeo"""
        try:
            with yt_dlp.YoutubeDL(self.ydl_opts) as ydl:
                info = ydl.extract_info(url, download=False)
                
                if not info:
                    return None
                
                # Extrair dados estruturados
                video_data = {
                    'video_id': info.get('id'),
                    'title': info.get('title'),
                    'description': info.get('description', ''),
                    'channel_name': info.get('uploader') or info.get('channel'),
                    'channel_id': info.get('uploader_id') or info.get('channel_id'),
                    'channel_url': info.get('uploader_url') or info.get('channel_url'),
                    'duration': info.get('duration', 0),
                    'view_count': info.get('view_count', 0),
                    'like_count': info.get('like_count', 0),
                    'subscriber_count': info.get('channel_follower_count', 0),
                    'upload_date': info.get('upload_date'),
                    'tags': info.get('tags', []),
                    'categories': info.get('categories', []),
                    'thumbnail_url': self._get_best_thumbnail(info.get('thumbnails', [])),
                    'video_url': url,
                    'language': info.get('language', 'pt'),
                    'total_comments': 0,  # Será atualizado depois
                    'has_transcript': False  # Será atualizado depois
                }
                
                return video_data
                
        except Exception as e:
            print(f"Erro ao extrair informações do vídeo: {e}")
            return None
    
    def extract_comments(self, url: str, max_comments: int = -1) -> List[Dict[str, Any]]:
        """Extrai TODOS os comentários do vídeo"""
        try:
            # Configuração específica para comentários
            comment_opts = {
                **self.ydl_opts,
                'getcomments': True,
                'writeinfojson': False,
            }
            
            with yt_dlp.YoutubeDL(comment_opts) as ydl:
                info = ydl.extract_info(url, download=False)
                
                if not info or 'comments' not in info:
                    return []
                
                comments = []
                raw_comments = info['comments']
                
                if max_comments > 0:
                    raw_comments = raw_comments[:max_comments]
                
                for comment in raw_comments:
                    comment_data = {
                        'comment_id': comment.get('id'),
                        'author': comment.get('author'),
                        'author_channel_id': comment.get('author_id'),
                        'text': comment.get('text', ''),
                        'like_count': comment.get('like_count', 0),
                        'reply_count': comment.get('reply_count', 0),
                        'published_at': comment.get('timestamp'),
                        'updated_at': comment.get('timestamp'),
                        'is_reply': comment.get('parent') is not None,
                        'parent_comment_id': comment.get('parent')
                    }
                    comments.append(comment_data)
                
                return comments
                
        except Exception as e:
            print(f"Erro ao extrair comentários: {e}")
            return []
    
    def extract_transcript(self, video_id: str) -> List[Dict[str, Any]]:
        """Extrai transcrição/legendas do vídeo com múltiplas estratégias"""
        try:
            print(f"🔍 Tentando extrair transcrição para vídeo: {video_id}")

            # Criar instância da API
            ytt_api = YouTubeTranscriptApi()

            # Estratégia 1: Tentar idiomas específicos
            languages_to_try = [
                ['pt'], ['pt-BR'], ['en'], ['en-US'],
                ['es'], ['fr'], ['de'], ['it']
            ]

            for lang_list in languages_to_try:
                try:
                    print(f"🔄 Tentando extrair com idioma: {lang_list[0]}")
                    fetched_transcript = ytt_api.fetch(video_id, languages=lang_list)
                    print(f"✅ Sucesso com idioma: {lang_list[0]} (tipo: {'automática' if fetched_transcript.is_generated else 'manual'})")
                    return self._convert_fetched_transcript(fetched_transcript)
                except NoTranscriptFound:
                    print(f"❌ Nenhuma transcrição encontrada para {lang_list[0]}")
                    continue
                except TranscriptsDisabled:
                    print(f"❌ Transcrições desabilitadas para este vídeo")
                    break
                except Exception as e:
                    print(f"❌ Falha com {lang_list[0]}: {e}")
                    continue

            # Estratégia 2: Sem especificar idioma (pega qualquer um disponível)
            try:
                print("🔄 Tentando sem especificar idioma")
                fetched_transcript = ytt_api.fetch(video_id)
                print(f"✅ Sucesso sem especificar idioma: {fetched_transcript.language_code} (tipo: {'automática' if fetched_transcript.is_generated else 'manual'})")
                return self._convert_fetched_transcript(fetched_transcript)
            except NoTranscriptFound:
                print("❌ Nenhuma transcrição encontrada")
            except TranscriptsDisabled:
                print("❌ Transcrições desabilitadas para este vídeo")
            except Exception as e:
                print(f"❌ Falha sem idioma: {e}")

            print("❌ Todas as estratégias falharam")
            return []

        except Exception as e:
            print(f"❌ Erro geral ao extrair transcrição: {e}")
            return []

    def _convert_fetched_transcript(self, fetched_transcript) -> List[Dict[str, Any]]:
        """Converte FetchedTranscript para formato padronizado"""
        result = []
        for snippet in fetched_transcript:
            result.append({
                'language': fetched_transcript.language_code,
                'text': snippet.text.strip(),
                'start': snippet.start,
                'duration': snippet.duration,
                'confidence': 0.95 if not fetched_transcript.is_generated else 0.8,
                'is_auto_generated': fetched_transcript.is_generated
            })
        return result

    def extract_transcript_with_whisper(self, video_id: str, model_size: str = "tiny", max_duration: int = 600) -> List[Dict[str, Any]]:
        """Extrai transcrição usando Whisper com opções otimizadas"""
        try:
            # Verificar se o whisper está disponível
            try:
                import whisper
            except ImportError:
                print("Whisper não está instalado. Use: pip install openai-whisper")
                return []

            # Configurar yt-dlp para baixar apenas o áudio com otimizações
            temp_dir = tempfile.mkdtemp()
            audio_file = os.path.join(temp_dir, f"{video_id}.%(ext)s")

            ydl_opts = {
                'format': 'worstaudio/worst',  # Qualidade menor = mais rápido
                'outtmpl': audio_file,
                'quiet': True,
                'no_warnings': True,
                'postprocessors': [{
                    'key': 'FFmpegExtractAudio',
                    'preferredcodec': 'mp3',
                    'preferredquality': '64',  # Qualidade baixa para velocidade
                }],
            }

            # Baixar áudio
            url = f"https://www.youtube.com/watch?v={video_id}"
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                info = ydl.extract_info(url, download=False)
                duration = info.get('duration', 0)

                # Limitar duração para evitar processamento muito longo
                if duration > max_duration:
                    print(f"Vídeo muito longo ({duration}s). Limitando a {max_duration}s")
                    ydl_opts['download_ranges'] = lambda info_dict, ydl: [{'start_time': 0, 'end_time': max_duration}]

                ydl.download([url])

            # Encontrar o arquivo de áudio baixado
            audio_files = [f for f in os.listdir(temp_dir) if f.startswith(video_id)]
            if not audio_files:
                return []

            audio_path = os.path.join(temp_dir, audio_files[0])

            # Carregar modelo Whisper otimizado
            print(f"Carregando modelo Whisper: {model_size}")
            model = whisper.load_model(model_size)

            # Transcrever com otimizações
            print("Iniciando transcrição...")
            result = model.transcribe(
                audio_path,
                language="pt",
                fp16=False,  # Usar FP32 para compatibilidade
                verbose=False,
                condition_on_previous_text=False,  # Mais rápido
                temperature=0,  # Determinístico
                compression_ratio_threshold=2.4,
                logprob_threshold=-1.0,
                no_speech_threshold=0.6
            )

            # Converter para formato compatível
            transcript = []
            for i, segment in enumerate(result["segments"]):
                transcript.append({
                    'text': segment['text'].strip(),
                    'start_time': segment['start'],
                    'duration': segment['end'] - segment['start'],
                    'confidence': 0.85 if model_size == "tiny" else 0.9,  # Ajustar confidence por modelo
                    'language': 'pt'
                })

            # Limpar arquivos temporários
            try:
                os.remove(audio_path)
                os.rmdir(temp_dir)
            except:
                pass

            print(f"Transcrição concluída: {len(transcript)} segmentos")
            return transcript

        except Exception as e:
            print(f"Erro ao extrair transcrição com Whisper: {e}")
            return []
    
    def extract_all_data(self, url: str, max_comments: int = -1) -> Dict[str, Any]:
        """Extrai TODOS os dados do vídeo"""
        print("🎬 Extraindo informações do vídeo...")
        video_info = self.extract_video_info(url)
        
        if not video_info:
            return {'success': False, 'error': 'Não foi possível extrair informações do vídeo'}
        
        video_id = video_info['video_id']
        
        print("💬 Extraindo comentários...")
        comments = self.extract_comments(url, max_comments)
        video_info['total_comments'] = len(comments)
        
        print("📝 Extraindo transcrição...")
        transcript = self.extract_transcript(video_id)
        video_info['has_transcript'] = len(transcript) > 0
        
        return {
            'success': True,
            'video_info': video_info,
            'comments': comments,
            'transcript': transcript,
            'stats': {
                'total_comments': len(comments),
                'has_transcript': len(transcript) > 0,
                'transcript_segments': len(transcript)
            }
        }
    
    def _get_best_thumbnail(self, thumbnails: List[Dict]) -> str:
        """Seleciona a melhor thumbnail disponível"""
        if not thumbnails:
            return ""
        
        # Ordenar por resolução (maior primeiro)
        sorted_thumbs = sorted(thumbnails, key=lambda x: x.get('width', 0) * x.get('height', 0), reverse=True)
        return sorted_thumbs[0].get('url', '')
    
    def format_duration(self, seconds: int) -> str:
        """Formata duração em segundos para HH:MM:SS"""
        if not seconds:
            return "00:00"
        
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        seconds = seconds % 60
        
        if hours > 0:
            return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
        else:
            return f"{minutes:02d}:{seconds:02d}"
    
    def format_number(self, number: int) -> str:
        """Formata números grandes (1.2M, 1.5K, etc.)"""
        if number >= 1_000_000:
            return f"{number/1_000_000:.1f}M"
        elif number >= 1_000:
            return f"{number/1_000:.1f}K"
        else:
            return str(number)


